#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例配置文件 - 演示如何使用AI小说创作系统
"""

# 示例小说配置
EXAMPLE_NOVEL_CONFIG = {
    "title": "剑修传说",
    "genre": "玄幻修仙",
    "theme": "自我救赎与身份认同的冲突",
    "main_plot": "废柴少年携带祖传玉佩，在宗门试炼中揭开自己身为上古剑修转世的秘密",
    "total_chapters": 100,
    "target_words_per_chapter": 3000,
    "style": "传统网文",
    "target_audience": "网络小说读者",
    "special_requirements": [
        "每章必须有冲突",
        "主角成长要循序渐进",
        "世界观要自洽",
        "伏笔要有回收"
    ]
}

# 快速开始配置（较短的小说用于测试）
QUICK_START_CONFIG = {
    "title": "测试小说",
    "genre": "都市",
    "theme": "成长与友情",
    "main_plot": "普通高中生意外获得超能力，在保护朋友的过程中成长",
    "total_chapters": 10,
    "target_words_per_chapter": 2000,
    "style": "轻松",
    "target_audience": "年轻读者"
}

# AI配置示例（需要根据实际情况修改）
AI_CONFIG_EXAMPLE = {
    "ai_models": {
        "primary": {
            "provider": "openai",
            "model": "gpt-4",
            "api_key": "your_api_key_here",  # 请替换为实际的API密钥
            "base_url": "https://api.openai.com/v1",
            "max_tokens": 4000,
            "temperature": 0.8
        },
        "local": {
            "provider": "ollama",
            "model": "qwen2:7b",
            "base_url": "http://localhost:11434",
            "max_tokens": 4000,
            "temperature": 0.8
        }
    }
}

def print_example_usage():
    """打印使用示例"""
    print("=== AI全自动写小说系统使用示例 ===\n")
    
    print("1. 基本使用流程：")
    print("   - 配置AI接口（编辑 config/ai_config.json）")
    print("   - 运行 python main.py")
    print("   - 选择创建新小说")
    print("   - 输入小说基本信息")
    print("   - 系统自动生成大纲、角色、世界观")
    print("   - 逐章生成或一次性生成完整小说\n")
    
    print("2. 配置文件说明：")
    print("   - config/ai_config.json: AI模型配置")
    print("   - config/system_config.json: 系统参数配置")
    print("   - templates/: 提示词模板")
    print("   - data/: 生成的小说数据存储\n")
    
    print("3. 示例小说配置：")
    for key, value in EXAMPLE_NOVEL_CONFIG.items():
        print(f"   {key}: {value}")
    print()
    
    print("4. 注意事项：")
    print("   - 首次使用需要配置AI API密钥")
    print("   - 建议先用较短的小说测试系统")
    print("   - 生成过程中可以随时中断和恢复")
    print("   - 支持人工干预和质量控制")

if __name__ == "__main__":
    print_example_usage()
