{"battle_scene": {"name": "战斗场景模板", "description": "用于生成战斗场景的提示词模板", "template": "描写{character_a}与{character_b}的对决，要求：\n1. 突出{skill_count}个专属技能细节（例：「{skill_example}」需描写{skill_details}）\n2. 环境互动：至少2处场景元素参与战斗（例：{env_interaction_examples}）\n3. 心理描写：穿插{character_a}的3次心理波动（从「{emotion_start}→{emotion_middle}→{emotion_end}」）\n4. 战斗节奏：{pace_description}\n5. 结果导向：{battle_outcome}", "variables": {"character_a": "主要战斗角色", "character_b": "对手角色", "skill_count": "技能展示数量", "skill_example": "技能名称示例", "skill_details": "技能细节描述", "env_interaction_examples": "环境互动示例", "emotion_start": "初始情绪", "emotion_middle": "中期情绪", "emotion_end": "结束情绪", "pace_description": "节奏控制", "battle_outcome": "战斗结果"}, "examples": {"skill_example": "冽风三连刺", "skill_details": "剑刃带起的空气爆鸣与地面冰痕", "env_interaction_examples": "利用屋顶积雪制造冰锥，砍断支撑梁柱引发崩塌", "emotion_start": "轻敌", "emotion_middle": "震惊", "emotion_end": "决绝"}}, "emotional_scene": {"name": "情感场景模板", "description": "用于生成情感场景的提示词模板", "template": "通过{sensory_detail_count}个感官细节表现{character}的{emotion}（例：用「{sensory_examples}」表现{emotion_description}），禁止直接使用{forbidden_words}（如{forbidden_examples}）\n\n要求：\n1. 感官层次：{sensory_layers}\n2. 环境映射：{environment_metaphor}\n3. 动作细节：{action_details}\n4. 内心独白：{inner_monologue}\n5. 情感递进：{emotion_progression}", "variables": {"sensory_detail_count": "感官细节数量", "character": "角色名称", "emotion": "目标情感", "sensory_examples": "感官细节示例", "emotion_description": "情感描述", "forbidden_words": "禁用词汇类型", "forbidden_examples": "禁用词汇示例", "sensory_layers": "感官层次要求", "environment_metaphor": "环境隐喻", "action_details": "动作细节", "inner_monologue": "内心独白", "emotion_progression": "情感递进"}, "examples": {"sensory_examples": "指尖划过泛黄信纸上的凹痕、窗外飘来的晚香玉花香、指甲陷入掌心的刺痛", "emotion_description": "对亡母的思念", "forbidden_examples": "悲伤、怀念", "sensory_layers": "视觉、听觉、触觉各一个", "environment_metaphor": "用环境变化反映内心状态"}}, "dialogue_scene": {"name": "对话场景模板", "description": "用于生成对话场景的提示词模板", "template": "{character_count}人对话场景，主题：{dialogue_theme}\n\n角色设定：\n{character_settings}\n\n对话要求：\n1. 语言特色：每个角色都要体现独特的说话方式\n2. 信息传递：{information_goals}\n3. 情感变化：{emotion_changes}\n4. 潜台词：{subtext_requirements}\n5. 节奏控制：{rhythm_control}\n\n场景氛围：{scene_atmosphere}", "variables": {"character_count": "对话人数", "dialogue_theme": "对话主题", "character_settings": "角色设定信息", "information_goals": "信息传递目标", "emotion_changes": "情感变化轨迹", "subtext_requirements": "潜台词要求", "rhythm_control": "节奏控制", "scene_atmosphere": "场景氛围"}}, "mystery_scene": {"name": "悬疑场景模板", "description": "用于生成悬疑场景的提示词模板", "template": "悬疑场景：{mystery_core}\n\n线索布局：\n1. 明线索：{obvious_clues}\n2. 暗线索：{hidden_clues}\n3. 红鲱鱼：{red_herrings}\n\n氛围营造：\n1. 环境描写：{environment_description}\n2. 心理暗示：{psychological_hints}\n3. 节奏控制：{pacing_control}\n\n角色反应：\n{character_reactions}\n\n悬念设置：{suspense_setup}", "variables": {"mystery_core": "悬疑核心", "obvious_clues": "明显线索", "hidden_clues": "隐藏线索", "red_herrings": "误导线索", "environment_description": "环境描写要求", "psychological_hints": "心理暗示", "pacing_control": "节奏控制", "character_reactions": "角色反应", "suspense_setup": "悬念设置"}}, "worldbuilding_scene": {"name": "世界观展示模板", "description": "用于展示世界观设定的提示词模板", "template": "世界观展示场景：{worldview_aspect}\n\n展示方式：\n1. 自然融入：通过{integration_method}自然展示设定\n2. 角色视角：从{character_perspective}的角度观察\n3. 细节层次：{detail_levels}\n\n设定要素：\n{setting_elements}\n\n避免事项：\n1. 不要生硬说教\n2. 不要信息堆砌\n3. 保持故事流畅性\n\n展示重点：{focus_points}", "variables": {"worldview_aspect": "世界观方面", "integration_method": "融入方式", "character_perspective": "角色视角", "detail_levels": "细节层次", "setting_elements": "设定要素", "focus_points": "展示重点"}}, "transition_scene": {"name": "过渡场景模板", "description": "用于生成过渡场景的提示词模板", "template": "过渡场景：从{scene_from}到{scene_to}\n\n过渡功能：\n1. 情节衔接：{plot_connection}\n2. 情绪调节：{mood_adjustment}\n3. 信息补充：{information_supplement}\n\n过渡手法：\n{transition_techniques}\n\n节奏要求：{pacing_requirements}\n\n重要元素：{key_elements}", "variables": {"scene_from": "起始场景", "scene_to": "目标场景", "plot_connection": "情节衔接", "mood_adjustment": "情绪调节", "information_supplement": "信息补充", "transition_techniques": "过渡手法", "pacing_requirements": "节奏要求", "key_elements": "重要元素"}}}