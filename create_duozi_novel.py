#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建多子多福系统文小说
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from main import NovelWritingSystem

def create_duozi_novel():
    """创建多子多福系统文"""
    print("🎉 开始创建多子多福系统文小说")
    print("=" * 50)
    
    # 多子多福系统文配置
    novel_config = {
        "title": "多子多福系统",
        "genre": "都市系统文",
        "theme": "家庭、责任与成长",
        "main_plot": "普通上班族林浩意外获得多子多福系统，每生一个孩子就能获得丰厚奖励，从此走上人生巅峰的故事",
        "protagonist": {
            "name": "林浩",
            "age": 28,
            "occupation": "程序员",
            "personality": "善良、负责任、内向",
            "background": "普通家庭出身，大学毕业后在互联网公司工作，月薪8000，单身，对未来迷茫"
        },
        "heroine": {
            "name": "苏晴雨", 
            "age": 26,
            "occupation": "小学教师",
            "personality": "温柔、善良、喜欢孩子",
            "background": "林浩的大学同学，一直对林浩有好感"
        },
        "system_setting": {
            "name": "多子多福系统",
            "function": "每生育一个孩子获得递增奖励",
            "rewards": "金钱、技能、身体强化、特殊道具等",
            "progression": "第一个孩子100万，第二个200万，以此类推"
        },
        "total_chapters": 100,
        "target_words_per_chapter": 3000,
        "setting": "现代都市",
        "tone": "轻松幽默，正能量"
    }
    
    try:
        # 创建系统实例
        system = NovelWritingSystem()
        print("✅ AI小说创作系统初始化完成")
        
        # 创建新小说项目
        print(f"📚 创建小说：《{novel_config['title']}》")
        print(f"   类型：{novel_config['genre']}")
        print(f"   主角：{novel_config['protagonist']['name']}")
        print(f"   女主：{novel_config['heroine']['name']}")
        print(f"   主线：{novel_config['main_plot']}")
        print()
        
        project_dir = system.create_new_novel(novel_config)
        print(f"✅ 项目创建完成：{project_dir}")
        print()
        
        # 生成第一章
        print("📝 开始生成第一章...")
        print("-" * 30)
        
        chapter = system.generate_chapter(project_dir, 1)
        
        if chapter and chapter.get('content'):
            print("✅ 第一章生成成功！")
            print(f"   标题：{chapter.get('title', '第一章')}")
            print(f"   字数：{len(chapter['content'])}")
            print()
            
            # 保存章节到单独文件
            chapter_file = project_dir / "第一章.txt"
            with open(chapter_file, 'w', encoding='utf-8') as f:
                f.write(f"《{novel_config['title']}》\n")
                f.write(f"第一章 {chapter.get('title', '开始')}\n\n")
                f.write(chapter['content'])
            
            print("📖 第一章内容：")
            print("=" * 50)
            print(f"第一章 {chapter.get('title', '开始')}")
            print()
            print(chapter['content'])
            print("=" * 50)
            
            print()
            print("📁 文件保存位置：")
            print(f"   项目目录：{project_dir}")
            print(f"   第一章文件：{chapter_file}")
            
            return project_dir
            
        else:
            print("❌ 第一章生成失败")
            return None
            
    except Exception as e:
        print(f"❌ 创建失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    project_dir = create_duozi_novel()
    
    if project_dir:
        print()
        print("🎊 多子多福系统文创建完成！")
        print("您可以继续使用系统生成更多章节，或查看项目文件。")
    else:
        print()
        print("😔 创建失败，请检查系统配置。")
