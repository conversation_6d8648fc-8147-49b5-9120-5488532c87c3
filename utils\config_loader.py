#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置加载器
"""

import json
import os
from pathlib import Path
from typing import Dict, Any

class ConfigLoader:
    """配置加载器类"""
    
    def __init__(self, config_dir="config"):
        self.config_dir = Path(config_dir)
        self._configs = {}
        self._load_all_configs()
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        config_files = {
            'ai': 'ai_config.json',
            'system': 'system_config.json'
        }
        
        for config_name, filename in config_files.items():
            config_path = self.config_dir / filename
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    self._configs[config_name] = json.load(f)
            else:
                self._configs[config_name] = {}
    
    def get_ai_config(self) -> Dict[str, Any]:
        """获取AI配置"""
        return self._configs.get('ai', {})
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        return self._configs.get('system', {})
    
    def get_config(self, config_name: str, default=None) -> Any:
        """获取指定配置"""
        return self._configs.get(config_name, default)
    
    def get_nested_config(self, *keys, default=None) -> Any:
        """获取嵌套配置值"""
        config = self._configs
        for key in keys:
            if isinstance(config, dict) and key in config:
                config = config[key]
            else:
                return default
        return config
    
    def update_config(self, config_name: str, config_data: Dict[str, Any]):
        """更新配置"""
        self._configs[config_name] = config_data
    
    def save_config(self, config_name: str):
        """保存配置到文件"""
        config_files = {
            'ai': 'ai_config.json',
            'system': 'system_config.json'
        }
        
        if config_name in config_files:
            config_path = self.config_dir / config_files[config_name]
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self._configs[config_name], f, ensure_ascii=False, indent=2)
