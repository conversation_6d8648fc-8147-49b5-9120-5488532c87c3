#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
世界观管理器 - 设定数据库管理
"""

import json
import uuid
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from utils.logger import LoggerMixin

@dataclass
class GeographicRegion:
    """地理区域"""
    name: str
    core_features: str
    special_rules: List[Dict[str, str]]  # {"rule": "规则", "effect": "效果"}
    climate: str
    inhabitants: List[str]
    key_locations: List[str]

@dataclass
class PowerSystem:
    """力量体系"""
    name: str
    levels: List[str]
    advancement_method: str
    special_abilities: List[Dict[str, str]]
    limitations: List[str]

@dataclass
class SocialStructure:
    """社会结构"""
    name: str
    hierarchy: List[Dict[str, str]]  # {"level": "等级", "description": "描述"}
    governance: str
    laws_and_customs: List[str]
    conflicts: List[str]

@dataclass
class HistoricalEvent:
    """历史事件"""
    name: str
    time_period: str
    description: str
    impact: str
    legacy_issues: List[str]
    related_characters: List[str]

@dataclass
class Worldview:
    """世界观"""
    name: str
    geography: List[GeographicRegion]
    power_systems: List[PowerSystem]
    social_structures: List[SocialStructure]
    history: List[HistoricalEvent]
    magic_rules: Dict[str, Any]
    technology_level: str
    created_at: str
    updated_at: str

class WorldviewManager(LoggerMixin):
    """世界观管理器"""
    
    def __init__(self, config):
        self.config = config
    
    def create_worldview(self, novel_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建世界观设定"""
        self.logger.info("开始创建世界观设定")
        
        # 创建地理设定
        geography = self._create_geography()
        
        # 创建力量体系
        power_systems = self._create_power_systems()
        
        # 创建社会结构
        social_structures = self._create_social_structures()
        
        # 创建历史背景
        history = self._create_history()
        
        # 创建魔法规则
        magic_rules = self._create_magic_rules()
        
        worldview = Worldview(
            name=f"{novel_config['title']}世界观",
            geography=geography,
            power_systems=power_systems,
            social_structures=social_structures,
            history=history,
            magic_rules=magic_rules,
            technology_level="古代修仙",
            created_at=self._get_timestamp(),
            updated_at=self._get_timestamp()
        )
        
        self.logger.info("世界观设定创建完成")
        return asdict(worldview)
    
    def _create_geography(self) -> List[GeographicRegion]:
        """创建地理设定"""
        regions = []
        
        # 苍澜大陆
        cang_lan = GeographicRegion(
            name="苍澜大陆",
            core_features="东部为魔法生物聚居的雾隐森林，西部是机械侏儒建造的齿轮城邦",
            special_rules=[
                {
                    "rule": "雾隐森林内火系魔法威力下降30%",
                    "effect": "声音传播距离缩短50%"
                },
                {
                    "rule": "齿轮城邦内机械装置效率提升20%",
                    "effect": "魔法波动会干扰精密机械"
                }
            ],
            climate="温带大陆性气候，四季分明",
            inhabitants=["人类", "精灵", "侏儒", "魔法生物"],
            key_locations=["剑宗", "雾隐森林", "齿轮城邦", "血河教总坛"]
        )
        regions.append(cang_lan)
        
        # 炎裔聚落
        yan_yi_area = GeographicRegion(
            name="熔火堡",
            core_features="建立在活火山口的炎裔独立聚落",
            special_rules=[
                {
                    "rule": "火系法术威力提升50%",
                    "effect": "冰系法术威力下降70%"
                },
                {
                    "rule": "高温环境对非炎裔造成持续伤害",
                    "effect": "炎裔在此地恢复速度加快"
                }
            ],
            climate="极热，常年高温",
            inhabitants=["炎裔"],
            key_locations=["熔火宫", "炎龙祭坛", "岩浆矿场"]
        )
        regions.append(yan_yi_area)
        
        return regions
    
    def _create_power_systems(self) -> List[PowerSystem]:
        """创建力量体系"""
        systems = []
        
        # 剑修体系
        sword_cultivation = PowerSystem(
            name="剑修体系",
            levels=[
                "练气期", "筑基期", "金丹期", "元婴期", 
                "化神期", "炼虚期", "合体期", "大乘期", "渡劫期"
            ],
            advancement_method="通过魔兽山脉的「心魂试炼」，感悟剑意，凝聚剑魂",
            special_abilities=[
                {
                    "name": "剑气外放",
                    "description": "将内力凝聚成剑气，远程攻击"
                },
                {
                    "name": "剑意共鸣",
                    "description": "与天地剑意产生共鸣，威力倍增"
                },
                {
                    "name": "剑魂觉醒",
                    "description": "觉醒前世剑修记忆和技能"
                }
            ],
            limitations=[
                "需要剑器作为媒介",
                "情绪波动会影响剑意稳定",
                "过度使用会损伤经脉"
            ]
        )
        systems.append(sword_cultivation)
        
        # 血河邪功
        blood_river_arts = PowerSystem(
            name="血河邪功",
            levels=[
                "血气入门", "血煞小成", "血海中成", 
                "血河大成", "血神化身"
            ],
            advancement_method="吸收他人精血，炼化血煞之气",
            special_abilities=[
                {
                    "name": "血煞侵蚀",
                    "description": "血气侵蚀敌人身体和神智"
                },
                {
                    "name": "血影分身",
                    "description": "用血气凝聚分身"
                }
            ],
            limitations=[
                "需要不断杀戮补充血气",
                "容易被正道功法克制",
                "修炼过度会失去理智"
            ]
        )
        systems.append(blood_river_arts)
        
        return systems
    
    def _create_social_structures(self) -> List[SocialStructure]:
        """创建社会结构"""
        structures = []
        
        # 剑宗结构
        sword_sect = SocialStructure(
            name="剑宗",
            hierarchy=[
                {"level": "宗主", "description": "统领全宗，决定重大事务"},
                {"level": "长老", "description": "管理各堂事务，指导弟子修炼"},
                {"level": "内门弟子", "description": "核心弟子，享有特殊待遇"},
                {"level": "外门弟子", "description": "普通弟子，需完成基础修炼"},
                {"level": "杂役弟子", "description": "负责宗门日常事务"}
            ],
            governance="长老会议制，重大决策需要三分之二长老同意",
            laws_and_customs=[
                "不得残杀同门",
                "不得泄露宗门秘法",
                "每月需完成规定任务",
                "尊师重道，维护宗门声誉"
            ],
            conflicts=[
                "内门与外门弟子的资源争夺",
                "不同长老派系的权力斗争",
                "与血河教的世代仇恨"
            ]
        )
        structures.append(sword_sect)
        
        # 齿轮城邦
        gear_city = SocialStructure(
            name="齿轮城邦",
            hierarchy=[
                {"level": "机械议会", "description": "由七名大工匠组成的最高决策机构"},
                {"level": "齿轮贵族", "description": "拥有大型工坊的富商家族"},
                {"level": "工匠", "description": "掌握机械技术的技术人员"},
                {"level": "平民", "description": "普通居民，需佩戴身份齿轮"}
            ],
            governance="议会与贵族共同治理，重视技术创新",
            laws_and_customs=[
                "所有居民必须佩戴身份齿轮",
                "技术创新者享有专利保护",
                "禁止在城内使用大型魔法",
                "每年举办机械竞技大会"
            ],
            conflicts=[
                "传统工艺与魔法技术的冲突",
                "贵族与平民的阶级矛盾",
                "资源分配不均引发的社会问题"
            ]
        )
        structures.append(gear_city)
        
        return structures
    
    def _create_history(self) -> List[HistoricalEvent]:
        """创建历史背景"""
        events = []
        
        # 炎魔之乱
        flame_demon_chaos = HistoricalEvent(
            name="炎魔之乱",
            time_period="百年前",
            description="上古炎魔苏醒，肆虐苍澜大陆，导致三分之一土地被熔岩覆盖",
            impact="催生了抗火种族「炎裔」，改变了大陆地理格局",
            legacy_issues=[
                "炎裔因外貌特征被人类城邦驱逐",
                "熔岩地带成为禁区，隐藏着炎魔遗迹",
                "火系魔法变得更加强大但难以控制"
            ],
            related_characters=["炎裔公主火舞", "上古剑修（主角前世）"]
        )
        events.append(flame_demon_chaos)
        
        # 剑宗建立
        sword_sect_founding = HistoricalEvent(
            name="剑宗建立",
            time_period="三百年前",
            description="上古剑修创立剑宗，传承剑道真谛",
            impact="成为大陆最强正道门派，维护正义秩序",
            legacy_issues=[
                "剑宗秘法失传，只留下残缺功法",
                "与血河教结下世代仇恨",
                "内部派系斗争影响团结"
            ],
            related_characters=["剑宗创始人（主角前世）", "血河教主（前世师弟）"]
        )
        events.append(sword_sect_founding)
        
        return events
    
    def _create_magic_rules(self) -> Dict[str, Any]:
        """创建魔法规则"""
        return {
            "基础规则": {
                "灵力来源": "天地灵气，通过修炼吸收转化",
                "施法媒介": "需要相应的法器或手势咒语",
                "消耗机制": "消耗修炼者的内力和精神力",
                "冷却时间": "强大法术需要恢复时间"
            },
            "元素相克": {
                "火克冰": "火系法术对冰系造成额外伤害",
                "水克火": "水系法术可以熄灭火焰",
                "土克水": "土系法术可以吸收水分",
                "风助火": "风系法术可以增强火系威力"
            },
            "环境影响": {
                "雾隐森林": "火系威力-30%，声音传播-50%",
                "熔火堡": "火系威力+50%，冰系威力-70%",
                "齿轮城邦": "魔法波动干扰精密机械"
            },
            "禁忌法术": {
                "血祭术": "以生命为代价的邪恶法术",
                "灵魂操控": "控制他人意识的禁术",
                "时空扭曲": "改变时间流速的危险法术"
            }
        }
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_location_info(self, worldview: Dict, location_name: str) -> Dict[str, Any]:
        """获取指定地点信息"""
        for region in worldview.get('geography', []):
            if location_name in region.get('key_locations', []):
                return {
                    'region': region,
                    'special_rules': region.get('special_rules', []),
                    'climate': region.get('climate', ''),
                    'inhabitants': region.get('inhabitants', [])
                }
        return {}
    
    def get_power_system_info(self, worldview: Dict, system_name: str) -> Dict[str, Any]:
        """获取力量体系信息"""
        for system in worldview.get('power_systems', []):
            if system.get('name') == system_name:
                return system
        return {}
    
    def check_magic_rules(self, worldview: Dict, spell_type: str, location: str) -> Dict[str, Any]:
        """检查魔法规则"""
        magic_rules = worldview.get('magic_rules', {})
        
        # 检查环境影响
        env_effects = magic_rules.get('环境影响', {})
        location_effects = env_effects.get(location, "无特殊影响")
        
        # 检查元素相克
        element_rules = magic_rules.get('元素相克', {})
        
        return {
            'location_effects': location_effects,
            'element_rules': element_rules,
            'basic_rules': magic_rules.get('基础规则', {}),
            'forbidden_spells': magic_rules.get('禁忌法术', {})
        }
