#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实AI生成多子多福系统文小说
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from main import NovelWritingSystem
from duozi_novel_config import DUOZI_NOVEL_CONFIG

def test_ai_connection():
    """测试AI连接"""
    print("🔍 测试AI连接...")
    
    try:
        from ai_interface.ai_client import AIClient
        from utils.config_loader import ConfigLoader
        
        config = ConfigLoader()
        ai_config = config.get_ai_config()
        client = AIClient(ai_config)
        
        test_prompt = "请回复'连接成功'"
        result = client.generate_text(test_prompt)
        
        if result and result.strip():
            print(f"✅ AI连接成功: {result[:50]}...")
            return True
        else:
            print("❌ AI返回空内容")
            return False
            
    except Exception as e:
        print(f"❌ AI连接失败: {str(e)}")
        return False

def generate_real_novel():
    """生成真实AI小说"""
    print("=" * 50)
    print("🎉 开始创建多子多福系统文（AI生成版本）")
    print("=" * 50)
    
    # 测试AI连接
    if not test_ai_connection():
        print("❌ AI连接失败，无法继续生成")
        return None
    
    try:
        # 创建系统实例
        print("初始化AI小说创作系统...")
        system = NovelWritingSystem()
        
        # 创建新小说项目
        print(f"创建新小说项目：{DUOZI_NOVEL_CONFIG['title']}")
        print(f"主角：{DUOZI_NOVEL_CONFIG['protagonist']['name']}")
        print(f"女主：{DUOZI_NOVEL_CONFIG['heroine']['name']}")
        print(f"主线：{DUOZI_NOVEL_CONFIG['main_plot']}")
        print()
        
        project_dir = system.create_new_novel(DUOZI_NOVEL_CONFIG)
        print(f"✅ 项目创建完成：{project_dir}")
        print()
        
        # 生成前五章
        print("开始生成前五章...")
        print("-" * 30)
        
        chapters_info = []
        
        for i in range(1, 6):
            chapter_config = DUOZI_NOVEL_CONFIG['first_five_chapters'][i-1]
            print(f"📝 正在生成第{i}章：{chapter_config['title']}")
            print(f"   主要事件：{', '.join(chapter_config['main_events'][:2])}...")
            
            try:
                chapter = system.generate_chapter(project_dir, i)
                
                # 保存章节信息
                chapter_info = {
                    'number': i,
                    'title': chapter.get('title', chapter_config['title']),
                    'word_count': len(chapter.get('content', '')),
                    'content_preview': chapter.get('content', '')[:200] + '...' if len(chapter.get('content', '')) > 200 else chapter.get('content', ''),
                    'status': 'success'
                }
                chapters_info.append(chapter_info)
                
                # 保存纯文本版本
                text_file = project_dir / "chapters" / f"chapter_{i:03d}.txt"
                with open(text_file, 'w', encoding='utf-8') as f:
                    f.write(f"第{i}章 {chapter_info['title']}\n\n")
                    f.write(chapter.get('content', ''))
                
                print(f"✅ 第{i}章生成完成")
                print(f"   标题：{chapter_info['title']}")
                print(f"   字数：{chapter_info['word_count']}")
                print(f"   预览：{chapter_info['content_preview']}")
                print()
                
            except Exception as e:
                print(f"❌ 第{i}章生成失败：{str(e)}")
                chapter_info = {
                    'number': i,
                    'title': chapter_config['title'],
                    'word_count': 0,
                    'error': str(e),
                    'status': 'failed'
                }
                chapters_info.append(chapter_info)
                print()
                continue
        
        # 生成总结报告
        print("=" * 50)
        print("📊 生成总结报告")
        print("=" * 50)
        
        successful_chapters = [ch for ch in chapters_info if ch['status'] == 'success']
        failed_chapters = [ch for ch in chapters_info if ch['status'] == 'failed']
        
        print(f"✅ 成功生成章节：{len(successful_chapters)}/5")
        print(f"❌ 失败章节：{len(failed_chapters)}/5")
        
        if successful_chapters:
            total_words = sum(ch['word_count'] for ch in successful_chapters)
            avg_words = total_words // len(successful_chapters) if successful_chapters else 0
            print(f"📝 总字数：{total_words}")
            print(f"📊 平均字数：{avg_words}")
        
        print()
        print("📁 项目文件位置：")
        print(f"   项目目录：{project_dir}")
        print(f"   章节文件：{project_dir}/chapters/")
        print(f"   项目配置：{project_dir}/project_config.json")
        
        # 保存生成报告
        report_file = project_dir / "generation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'novel_config': DUOZI_NOVEL_CONFIG,
                'chapters_info': chapters_info,
                'summary': {
                    'total_chapters': len(chapters_info),
                    'successful_chapters': len(successful_chapters),
                    'failed_chapters': len(failed_chapters),
                    'total_words': sum(ch.get('word_count', 0) for ch in successful_chapters),
                    'ai_generated': True,
                    'model_used': 'claude-3-7-sonnet'
                },
                'generated_at': datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
        
        print(f"   生成报告：{report_file}")
        print()
        
        if successful_chapters:
            print("🎉 多子多福系统文前五章AI生成完成！")
            print("这是真正由AI创作的内容，每章都是独特的！")
            
            # 显示章节概览
            print("\n📖 章节概览：")
            for chapter in successful_chapters:
                print(f"   第{chapter['number']}章：{chapter['title']} ({chapter['word_count']}字)")
        else:
            print("⚠️ 章节生成遇到问题，请检查AI配置和网络连接。")
        
        return project_dir
            
    except Exception as e:
        print(f"❌ 系统错误：{str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    project_dir = generate_real_novel()
    
    if project_dir:
        print(f"\n🎊 恭喜！您的多子多福系统文已经生成完成！")
        print(f"📂 项目位置：{project_dir}")
        print(f"💡 您可以继续使用系统生成更多章节，或者手动编辑现有内容。")
    else:
        print(f"\n😔 生成失败，请检查配置后重试。")
