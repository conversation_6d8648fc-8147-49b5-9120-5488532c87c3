#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
质量控制器 - 自动校验与风险控制
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from utils.logger import LoggerMixin

@dataclass
class QualityIssue:
    """质量问题"""
    type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    location: str
    suggestion: str

@dataclass
class QualityReport:
    """质量报告"""
    chapter_number: int
    passed: bool
    overall_score: float
    issues: List[QualityIssue]
    checks_performed: List[str]
    timestamp: str

class QualityController(LoggerMixin):
    """质量控制器"""
    
    def __init__(self, config):
        self.config = config
        self.quality_config = config.get_nested_config('system', 'quality_control', default={})
        self.creativity_config = config.get_nested_config('system', 'creativity', default={})
        self.risk_config = config.get_nested_config('system', 'risk_management', default={})
        
        # 质量阈值
        self.quality_threshold = self.quality_config.get('quality_threshold', 0.7)
        
        # 禁用词汇
        self.forbidden_words = self.creativity_config.get('forbidden_words', {})
    
    def check_chapter(self, chapter: Dict[str, Any], project_config: Dict[str, Any]) -> Dict[str, Any]:
        """检查章节质量"""
        self.logger.info(f"开始质量检查：第{chapter['number']}章")
        
        issues = []
        checks_performed = []
        
        # 1. 关键元素检查
        if self.quality_config.get('auto_check_enabled', True):
            element_issues = self._check_mandatory_elements(chapter)
            issues.extend(element_issues)
            checks_performed.append('mandatory_elements')
        
        # 2. OOC检测
        if self.quality_config.get('ooc_detection', True):
            ooc_issues = self._check_character_consistency(chapter, project_config)
            issues.extend(ooc_issues)
            checks_performed.append('character_consistency')
        
        # 3. 情节一致性检查
        if self.quality_config.get('plot_consistency', True):
            plot_issues = self._check_plot_consistency(chapter, project_config)
            issues.extend(plot_issues)
            checks_performed.append('plot_consistency')
        
        # 4. 伏笔追踪
        if self.quality_config.get('foreshadowing_tracking', True):
            foreshadowing_issues = self._check_foreshadowing(chapter, project_config)
            issues.extend(foreshadowing_issues)
            checks_performed.append('foreshadowing_tracking')
        
        # 5. 语言质量检查
        language_issues = self._check_language_quality(chapter)
        issues.extend(language_issues)
        checks_performed.append('language_quality')
        
        # 6. 风险检测
        risk_issues = self._check_risks(chapter, project_config)
        issues.extend(risk_issues)
        checks_performed.append('risk_detection')
        
        # 计算总体评分
        overall_score = self._calculate_overall_score(issues)
        
        # 判断是否通过
        passed = overall_score >= self.quality_threshold and not any(
            issue.severity == 'critical' for issue in issues
        )
        
        report = QualityReport(
            chapter_number=chapter['number'],
            passed=passed,
            overall_score=overall_score,
            issues=issues,
            checks_performed=checks_performed,
            timestamp=self._get_timestamp()
        )
        
        self.logger.info(f"质量检查完成，评分：{overall_score:.2f}，通过：{passed}")
        return asdict(report)
    
    def _check_mandatory_elements(self, chapter: Dict[str, Any]) -> List[QualityIssue]:
        """检查强制元素"""
        issues = []
        content = chapter.get('content', '')
        mandatory_elements = chapter.get('mandatory_elements', [])
        
        for element in mandatory_elements:
            if '伏笔：' in element:
                foreshadowing_content = element.replace('伏笔：', '').strip()
                if not self._contains_element(content, foreshadowing_content):
                    issues.append(QualityIssue(
                        type='missing_mandatory_element',
                        severity='high',
                        description=f'缺少必需的伏笔元素：{foreshadowing_content}',
                        location='全文',
                        suggestion=f'请在章节中加入相关描述：{foreshadowing_content}'
                    ))
            
            elif '设定植入：' in element:
                setting_content = element.replace('设定植入：', '').strip()
                if not self._contains_element(content, setting_content):
                    issues.append(QualityIssue(
                        type='missing_mandatory_element',
                        severity='medium',
                        description=f'缺少必需的设定植入：{setting_content}',
                        location='全文',
                        suggestion=f'请在章节中展示相关设定：{setting_content}'
                    ))
        
        return issues
    
    def _check_character_consistency(self, chapter: Dict[str, Any], project_config: Dict[str, Any]) -> List[QualityIssue]:
        """检查角色一致性（OOC检测）"""
        issues = []
        content = chapter.get('content', '')
        characters = project_config.get('characters', {})
        
        # 检查主角行为一致性
        for char_id, char_data in characters.items():
            char_name = char_data['basic_info']['full_name']
            
            if char_name in content:
                # 检查性格触发机制
                personality_triggers = char_data.get('behavior', {}).get('personality_triggers', [])
                for trigger in personality_triggers:
                    trigger_word = trigger.get('trigger', '')
                    expected_behavior = trigger.get('behavior', '')
                    
                    if trigger_word in content and expected_behavior:
                        # 简化检查：如果触发词出现但没有相应行为描述
                        if not self._contains_behavior_description(content, expected_behavior):
                            issues.append(QualityIssue(
                                type='character_ooc',
                                severity='high',
                                description=f'{char_name}的行为与设定不符：触发"{trigger_word}"时应该{expected_behavior}',
                                location=f'涉及{char_name}的情节',
                                suggestion=f'请修正{char_name}的行为描述，使其符合性格设定'
                            ))
                
                # 检查价值观冲突
                value_conflicts = char_data.get('behavior', {}).get('value_conflicts', [])
                for conflict in value_conflicts:
                    scenario = conflict.get('scenario', '')
                    if self._scenario_matches(content, scenario):
                        # 检查是否体现了内心冲突
                        if not self._contains_internal_conflict(content, char_name):
                            issues.append(QualityIssue(
                                type='missing_value_conflict',
                                severity='medium',
                                description=f'{char_name}在冲突场景中缺少内心挣扎的描述',
                                location=f'涉及{char_name}的冲突情节',
                                suggestion=f'请添加{char_name}的内心冲突描述'
                            ))
        
        return issues
    
    def _check_plot_consistency(self, chapter: Dict[str, Any], project_config: Dict[str, Any]) -> List[QualityIssue]:
        """检查情节一致性"""
        issues = []
        content = chapter.get('content', '')
        
        # 检查世界观规则一致性
        worldview = project_config.get('worldview', {})
        magic_rules = worldview.get('magic_rules', {})
        
        # 检查魔法使用是否符合规则
        forbidden_spells = magic_rules.get('禁忌法术', {})
        for spell_name in forbidden_spells.keys():
            if spell_name in content:
                issues.append(QualityIssue(
                    type='worldview_violation',
                    severity='high',
                    description=f'使用了禁忌法术：{spell_name}',
                    location='魔法使用场景',
                    suggestion='请修改为允许的法术，或提供合理的解释'
                ))
        
        # 检查力量体系一致性
        power_systems = worldview.get('power_systems', [])
        for system in power_systems:
            limitations = system.get('limitations', [])
            for limitation in limitations:
                if self._violates_limitation(content, limitation):
                    issues.append(QualityIssue(
                        type='power_system_violation',
                        severity='medium',
                        description=f'违反了力量体系限制：{limitation}',
                        location='力量使用场景',
                        suggestion=f'请遵守力量体系规则：{limitation}'
                    ))
        
        return issues
    
    def _check_foreshadowing(self, chapter: Dict[str, Any], project_config: Dict[str, Any]) -> List[QualityIssue]:
        """检查伏笔管理"""
        issues = []
        
        # 检查伏笔埋设
        chapter_log = chapter.get('log', {})
        foreshadowing_mgmt = chapter_log.get('foreshadowing_management', {})
        new_clues = foreshadowing_mgmt.get('new_clues', [])
        
        # 检查伏笔质量
        for clue in new_clues:
            if not clue.get('significance'):
                issues.append(QualityIssue(
                    type='weak_foreshadowing',
                    severity='low',
                    description='伏笔缺少明确的后续意义说明',
                    location='伏笔埋设处',
                    suggestion='请为伏笔添加清晰的后续关联说明'
                ))
        
        return issues
    
    def _check_language_quality(self, chapter: Dict[str, Any]) -> List[QualityIssue]:
        """检查语言质量"""
        issues = []
        content = chapter.get('content', '')
        
        # 检查禁用词汇
        for scene_type, forbidden_list in self.forbidden_words.items():
            for word in forbidden_list:
                if word in content:
                    issues.append(QualityIssue(
                        type='forbidden_word',
                        severity='medium',
                        description=f'使用了禁用词汇："{word}"（{scene_type}场景）',
                        location='全文',
                        suggestion=f'请用更生动的描述替换"{word}"'
                    ))
        
        # 检查重复词汇
        repeated_words = self._find_repeated_words(content)
        for word, count in repeated_words.items():
            if count > 5:  # 阈值可配置
                issues.append(QualityIssue(
                    type='word_repetition',
                    severity='low',
                    description=f'词汇"{word}"重复使用{count}次',
                    location='全文',
                    suggestion=f'请减少"{word}"的使用频率，增加表达多样性'
                ))
        
        # 检查句式多样性
        if self._lacks_sentence_variety(content):
            issues.append(QualityIssue(
                type='sentence_monotony',
                severity='low',
                description='句式缺乏变化，表达单调',
                location='全文',
                suggestion='请增加句式的多样性，使用不同的句型结构'
            ))
        
        return issues
    
    def _check_risks(self, chapter: Dict[str, Any], project_config: Dict[str, Any]) -> List[QualityIssue]:
        """检查风险事件"""
        issues = []
        content = chapter.get('content', '')
        
        dangerous_events = self.risk_config.get('dangerous_events', [])
        
        for event in dangerous_events:
            if self._contains_dangerous_event(content, event):
                issues.append(QualityIssue(
                    type='dangerous_event',
                    severity='critical',
                    description=f'检测到高危事件：{event}',
                    location='相关情节',
                    suggestion='请确认此事件是否符合剧情需要，或考虑修改'
                ))
        
        return issues
    
    def _calculate_overall_score(self, issues: List[QualityIssue]) -> float:
        """计算总体评分"""
        if not issues:
            return 1.0
        
        # 根据问题严重程度计算扣分
        severity_weights = {
            'low': 0.05,
            'medium': 0.15,
            'high': 0.3,
            'critical': 0.5
        }
        
        total_deduction = sum(severity_weights.get(issue.severity, 0.1) for issue in issues)
        score = max(0.0, 1.0 - total_deduction)
        
        return score
    
    # 辅助方法
    def _contains_element(self, content: str, element: str) -> bool:
        """检查内容是否包含指定元素"""
        # 简化实现，实际应该使用更智能的匹配
        keywords = element.split()
        return any(keyword in content for keyword in keywords)
    
    def _contains_behavior_description(self, content: str, behavior: str) -> bool:
        """检查是否包含行为描述"""
        # 简化实现
        behavior_keywords = behavior.split()
        return any(keyword in content for keyword in behavior_keywords)
    
    def _scenario_matches(self, content: str, scenario: str) -> bool:
        """检查场景是否匹配"""
        # 简化实现
        scenario_keywords = scenario.split()
        return any(keyword in content for keyword in scenario_keywords)
    
    def _contains_internal_conflict(self, content: str, char_name: str) -> bool:
        """检查是否包含内心冲突描述"""
        # 简化实现，寻找内心冲突的关键词
        conflict_keywords = ['挣扎', '犹豫', '矛盾', '纠结', '思考', '内心']
        char_context = self._extract_character_context(content, char_name)
        return any(keyword in char_context for keyword in conflict_keywords)
    
    def _extract_character_context(self, content: str, char_name: str) -> str:
        """提取角色相关的上下文"""
        # 简化实现，实际应该更精确
        sentences = content.split('。')
        char_sentences = [s for s in sentences if char_name in s]
        return '。'.join(char_sentences)
    
    def _violates_limitation(self, content: str, limitation: str) -> bool:
        """检查是否违反限制"""
        # 简化实现
        return False  # 需要根据具体限制实现检查逻辑
    
    def _contains_dangerous_event(self, content: str, event: str) -> bool:
        """检查是否包含危险事件"""
        event_keywords = {
            'character_death': ['死亡', '死去', '身亡', '丧命'],
            'worldview_change': ['世界观', '规则改变', '法则变化'],
            'timeline_disruption': ['时间', '穿越', '回到过去']
        }
        
        keywords = event_keywords.get(event, [])
        return any(keyword in content for keyword in keywords)
    
    def _find_repeated_words(self, content: str) -> Dict[str, int]:
        """查找重复词汇"""
        # 简化实现，实际应该使用更好的分词
        words = re.findall(r'[\u4e00-\u9fff]+', content)  # 提取中文词汇
        word_count = {}
        for word in words:
            if len(word) >= 2:  # 只统计2字以上的词
                word_count[word] = word_count.get(word, 0) + 1
        
        return {word: count for word, count in word_count.items() if count > 3}
    
    def _lacks_sentence_variety(self, content: str) -> bool:
        """检查句式多样性"""
        sentences = content.split('。')
        if len(sentences) < 10:
            return False
        
        # 简化检查：统计句子长度的方差
        lengths = [len(s) for s in sentences if s.strip()]
        if not lengths:
            return False
        
        avg_length = sum(lengths) / len(lengths)
        variance = sum((l - avg_length) ** 2 for l in lengths) / len(lengths)
        
        # 如果方差太小，说明句式单调
        return variance < 100  # 阈值可调整
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
