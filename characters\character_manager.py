#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人物卡管理器 - 构建可驱动的角色行为模型
"""

import json
import uuid
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from utils.logger import LoggerMixin

@dataclass
class BasicInfo:
    """基础信息层"""
    full_name: str
    nickname: str
    age: int
    appearance: str
    distinctive_features: str
    profession: str
    hidden_identity: str

@dataclass
class MotivationLayer:
    """动机层"""
    core_desire: str
    secondary_goals: List[str]
    fears: List[Dict[str, str]]  # {"trigger": "条件", "reaction": "反应"}
    obsessions: List[Dict[str, str]]  # {"item": "物品", "meaning": "意义"}

@dataclass
class BehaviorLayer:
    """行为层"""
    personality_triggers: List[Dict[str, str]]  # {"trigger": "触发词", "state": "状态", "behavior": "行为表现"}
    habits: Dict[str, List[str]]  # {"actions": [], "speech": []}
    value_conflicts: List[Dict[str, str]]  # {"scenario": "场景", "conflict": "冲突描述"}

@dataclass
class Character:
    """角色完整信息"""
    id: str
    basic_info: BasicInfo
    motivation: MotivationLayer
    behavior: BehaviorLayer
    relationships: Dict[str, Dict[str, Any]]  # 与其他角色的关系
    development_arc: List[Dict[str, Any]]  # 角色发展弧线
    current_state: Dict[str, Any]  # 当前状态
    created_at: str
    updated_at: str

class CharacterManager(LoggerMixin):
    """人物卡管理器"""
    
    def __init__(self, config):
        self.config = config
        self.character_config = config.get_nested_config('system', 'characters', default={})
    
    def create_main_characters(self, novel_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建主要角色"""
        self.logger.info("开始创建主要角色")
        
        characters = {}
        
        # 创建主角
        protagonist = self._create_protagonist(novel_config)
        characters[protagonist.id] = asdict(protagonist)
        
        # 创建主要配角
        supporting_chars = self._create_supporting_characters(novel_config)
        for char in supporting_chars:
            characters[char.id] = asdict(char)
        
        # 建立角色关系网
        self._establish_relationships(characters)
        
        self.logger.info(f"角色创建完成，共{len(characters)}个主要角色")
        return characters
    
    def _create_protagonist(self, novel_config: Dict) -> Character:
        """创建主角"""
        # 基础信息
        basic_info = BasicInfo(
            full_name="林砚冰",
            nickname="小冰",
            age=18,
            appearance="清秀少年，身材偏瘦，黑发黑眸",
            distinctive_features="左眼角有泪痣，总穿绣着银线鸢尾的青衫",
            profession="剑宗外门弟子",
            hidden_identity="上古剑修残魂宿主"
        )
        
        # 动机层
        motivation = MotivationLayer(
            core_desire="证明自己不是「灾星转世」，获得母亲的认可",
            secondary_goals=[
                "三个月内进入剑宗内门试炼前10名",
                "掌握祖传剑法的真正奥义",
                "找到母亲失踪的真相"
            ],
            fears=[
                {
                    "trigger": "听到锁链断裂声",
                    "reaction": "引发panic发作（童年被囚禁记忆）"
                },
                {
                    "trigger": "被人称为灾星",
                    "reaction": "情绪失控，剑意不稳"
                }
            ],
            obsessions=[
                {
                    "item": "母亲留下的断剑穗",
                    "meaning": "坚信集齐七段可复活母亲"
                }
            ]
        )
        
        # 行为层
        behavior = BehaviorLayer(
            personality_triggers=[
                {
                    "trigger": "和你父亲一样是个废物",
                    "state": "暴怒",
                    "behavior": "瞳孔变红，握剑的手青筋暴起，语言风格从温和转为冷硬"
                },
                {
                    "trigger": "提及母亲",
                    "state": "悲伤",
                    "behavior": "下意识摸向胸前的断剑穗，声音变得轻柔"
                }
            ],
            habits={
                "actions": [
                    "思考时会摩挲剑柄上的云纹雕刻",
                    "紧张时会整理衣袖",
                    "习惯在清晨练剑"
                ],
                "speech": [
                    "常用反问句「难道不是这样吗？」",
                    "紧张时会重复尾字",
                    "对长辈使用敬语"
                ]
            },
            value_conflicts=[
                {
                    "scenario": "同伴要求屠杀被控制的平民",
                    "conflict": "在「保护同伴」与「坚守不杀原则」间挣扎"
                }
            ]
        )
        
        return Character(
            id=str(uuid.uuid4()),
            basic_info=basic_info,
            motivation=motivation,
            behavior=behavior,
            relationships={},
            development_arc=self._create_protagonist_arc(),
            current_state={
                "cultivation_level": "练气三层",
                "emotional_state": "迷茫但坚定",
                "key_items": ["祖传玉佩", "断剑穗"],
                "known_secrets": [],
                "loyalty_values": {}
            },
            created_at=self._get_timestamp(),
            updated_at=self._get_timestamp()
        )
    
    def _create_supporting_characters(self, novel_config: Dict) -> List[Character]:
        """创建主要配角"""
        characters = []
        
        # 神秘师兄楚离
        chu_li = self._create_chu_li()
        characters.append(chu_li)
        
        # 剑宗长老叶默生
        ye_mo_sheng = self._create_ye_mo_sheng()
        characters.append(ye_mo_sheng)
        
        # 反派首领血河教主
        blood_river_master = self._create_blood_river_master()
        characters.append(blood_river_master)
        
        # 炎裔少女火舞
        huo_wu = self._create_huo_wu()
        characters.append(huo_wu)
        
        return characters
    
    def _create_chu_li(self) -> Character:
        """创建楚离"""
        basic_info = BasicInfo(
            full_name="楚离",
            nickname="离师兄",
            age=22,
            appearance="俊美青年，剑眉星目，气质冷峻",
            distinctive_features="右手腕有一道细长疤痕，喜穿黑色劲装",
            profession="剑宗内门弟子",
            hidden_identity="剑宗暗桩，奉命保护主角"
        )
        
        motivation = MotivationLayer(
            core_desire="完成师父的遗愿，保护林砚冰",
            secondary_goals=[
                "隐藏真实身份",
                "调查血河教的阴谋",
                "提升自身实力"
            ],
            fears=[
                {
                    "trigger": "身份暴露",
                    "reaction": "变得更加警惕和冷漠"
                }
            ],
            obsessions=[
                {
                    "item": "师父的遗书",
                    "meaning": "唯一的行动指南"
                }
            ]
        )
        
        behavior = BehaviorLayer(
            personality_triggers=[
                {
                    "trigger": "有人威胁林砚冰",
                    "state": "保护模式",
                    "behavior": "立即出手，不计后果"
                }
            ],
            habits={
                "actions": ["习惯观察周围环境", "喜欢站在阴影中"],
                "speech": ["言简意赅", "很少主动开口"]
            },
            value_conflicts=[
                {
                    "scenario": "保护主角与完成任务冲突",
                    "conflict": "在忠诚与理智间选择"
                }
            ]
        )
        
        return Character(
            id=str(uuid.uuid4()),
            basic_info=basic_info,
            motivation=motivation,
            behavior=behavior,
            relationships={},
            development_arc=[],
            current_state={
                "cultivation_level": "筑基后期",
                "emotional_state": "冷静克制",
                "key_items": ["师父遗书", "暗桩令牌"],
                "known_secrets": ["主角真实身份", "血河教计划"],
                "loyalty_values": {"林砚冰": 90, "剑宗": 70}
            },
            created_at=self._get_timestamp(),
            updated_at=self._get_timestamp()
        )
    
    def _create_ye_mo_sheng(self) -> Character:
        """创建叶默生"""
        # 简化实现，实际应该更详细
        basic_info = BasicInfo(
            full_name="叶默生",
            nickname="叶长老",
            age=45,
            appearance="中年男子，须发半白，眼神深邃",
            distinctive_features="左手缺少小指，胸前有剑伤疤痕",
            profession="剑宗长老",
            hidden_identity="林砚冰的生父"
        )
        
        # 其他属性简化...
        return Character(
            id=str(uuid.uuid4()),
            basic_info=basic_info,
            motivation=MotivationLayer("", [], [], []),
            behavior=BehaviorLayer([], {}, []),
            relationships={},
            development_arc=[],
            current_state={},
            created_at=self._get_timestamp(),
            updated_at=self._get_timestamp()
        )
    
    def _create_blood_river_master(self) -> Character:
        """创建血河教主"""
        # 简化实现
        basic_info = BasicInfo(
            full_name="血河教主",
            nickname="血主",
            age=60,
            appearance="阴鸷老者，血红长袍，气息邪恶",
            distinctive_features="双眼血红，指甲如刀",
            profession="血河教教主",
            hidden_identity="林砚冰前世的师弟"
        )
        
        return Character(
            id=str(uuid.uuid4()),
            basic_info=basic_info,
            motivation=MotivationLayer("", [], [], []),
            behavior=BehaviorLayer([], {}, []),
            relationships={},
            development_arc=[],
            current_state={},
            created_at=self._get_timestamp(),
            updated_at=self._get_timestamp()
        )
    
    def _create_huo_wu(self) -> Character:
        """创建火舞"""
        # 简化实现
        basic_info = BasicInfo(
            full_name="火舞",
            nickname="舞儿",
            age=17,
            appearance="炎裔少女，火红长发，皮肤微红",
            distinctive_features="额头有火焰印记，体温偏高",
            profession="炎裔战士",
            hidden_identity="炎裔公主"
        )
        
        return Character(
            id=str(uuid.uuid4()),
            basic_info=basic_info,
            motivation=MotivationLayer("", [], [], []),
            behavior=BehaviorLayer([], {}, []),
            relationships={},
            development_arc=[],
            current_state={},
            created_at=self._get_timestamp(),
            updated_at=self._get_timestamp()
        )
    
    def _establish_relationships(self, characters: Dict[str, Any]):
        """建立角色关系网"""
        # 找到主角
        protagonist_id = None
        for char_id, char_data in characters.items():
            if char_data['basic_info']['full_name'] == '林砚冰':
                protagonist_id = char_id
                break
        
        if not protagonist_id:
            return
        
        # 建立关系（简化实现）
        for char_id, char_data in characters.items():
            if char_id != protagonist_id:
                char_name = char_data['basic_info']['full_name']
                
                # 为主角添加与其他角色的关系
                if char_name == '楚离':
                    characters[protagonist_id]['relationships'][char_id] = {
                        "name": char_name,
                        "surface_relation": "师兄弟",
                        "hidden_relation": "保护者",
                        "trust_level": 60,
                        "key_events": []
                    }
                # 可以继续添加其他关系...
    
    def _create_protagonist_arc(self) -> List[Dict[str, Any]]:
        """创建主角发展弧线"""
        return [
            {
                "stage": "初期",
                "chapters": [1, 30],
                "description": "自卑迷茫的废柴弟子",
                "key_changes": ["发现天赋", "建立信心"],
                "emotional_state": "迷茫→希望"
            },
            {
                "stage": "中期",
                "chapters": [31, 70],
                "description": "逐渐成长的剑修",
                "key_changes": ["掌握力量", "面对真相"],
                "emotional_state": "希望→冲突"
            },
            {
                "stage": "后期",
                "chapters": [71, 100],
                "description": "觉醒的上古剑修",
                "key_changes": ["接受身份", "完成蜕变"],
                "emotional_state": "冲突→成熟"
            }
        ]
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_character_state(self, characters: Dict, character_id: str, chapter_num: int) -> Dict[str, Any]:
        """获取角色在指定章节的状态"""
        if character_id not in characters:
            return {}
        
        character = characters[character_id]
        
        # 根据章节数调整角色状态
        current_state = character['current_state'].copy()
        
        # 根据发展弧线调整状态
        for arc_stage in character.get('development_arc', []):
            if arc_stage['chapters'][0] <= chapter_num <= arc_stage['chapters'][1]:
                current_state['development_stage'] = arc_stage['stage']
                current_state['emotional_state'] = arc_stage['emotional_state']
                break
        
        return current_state
    
    def update_character_relationship(self, characters: Dict, char1_id: str, char2_id: str, 
                                    relationship_change: Dict) -> Dict:
        """更新角色关系"""
        if char1_id in characters and char2_id in characters[char1_id]['relationships']:
            characters[char1_id]['relationships'][char2_id].update(relationship_change)
            characters[char1_id]['updated_at'] = self._get_timestamp()
        
        return characters
