#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
尝试不同的API参数
"""

import requests
import json

def try_different_configs():
    """尝试不同的配置"""
    base_url = "https://lazy-bear-13.deno.dev/v1"
    api_key = "awdsadwasdas"
    
    configs = [
        {
            "name": "标准配置",
            "headers": {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            },
            "data": {
                "model": "claude-3-sonnet-20240229",
                "messages": [
                    {"role": "user", "content": "你好，请回复一句话"}
                ],
                "max_tokens": 100,
                "temperature": 0.8
            }
        },
        {
            "name": "简化配置",
            "headers": {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            },
            "data": {
                "model": "claude-3-sonnet-20240229",
                "messages": [
                    {"role": "user", "content": "你好"}
                ],
                "max_tokens": 50
            }
        },
        {
            "name": "GPT模型",
            "headers": {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            },
            "data": {
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "user", "content": "你好"}
                ],
                "max_tokens": 50
            }
        },
        {
            "name": "Claude-3-haiku",
            "headers": {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            },
            "data": {
                "model": "claude-3-haiku-20240307",
                "messages": [
                    {"role": "user", "content": "你好"}
                ],
                "max_tokens": 50
            }
        },
        {
            "name": "添加系统消息",
            "headers": {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            },
            "data": {
                "model": "claude-3-sonnet-20240229",
                "messages": [
                    {"role": "system", "content": "你是一个有用的助手"},
                    {"role": "user", "content": "你好"}
                ],
                "max_tokens": 50
            }
        }
    ]
    
    for config in configs:
        print(f"\n{'='*50}")
        print(f"测试配置: {config['name']}")
        print(f"{'='*50}")
        
        try:
            response = requests.post(
                f"{base_url}/chat/completions",
                headers=config['headers'],
                json=config['data'],
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查usage
                usage = result.get('usage', {})
                print(f"Token使用: {usage}")
                
                # 检查内容
                choices = result.get('choices', [])
                if choices:
                    content = choices[0].get('message', {}).get('content', '')
                    print(f"内容长度: {len(content)}")
                    print(f"内容: '{content}'")
                    
                    if content.strip():
                        print("✅ 成功获取内容!")
                        return config  # 返回成功的配置
                    else:
                        print("❌ 内容为空")
                else:
                    print("❌ 没有choices")
            else:
                print(f"❌ 请求失败: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
    
    return None

if __name__ == "__main__":
    print("尝试不同的API配置...")
    success_config = try_different_configs()
    
    if success_config:
        print(f"\n🎉 找到可用配置: {success_config['name']}")
        print("建议使用此配置更新系统")
    else:
        print("\n❌ 所有配置都失败了")
