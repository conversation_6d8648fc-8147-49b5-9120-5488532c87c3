{"outline": {"title": "多子多福系统", "main_plot": "普通上班族林浩意外获得多子多福系统，每生一个孩子就能获得丰厚奖励，从此走上人生巅峰的故事", "theme_core": "家庭责任与个人成长的平衡", "total_chapters": 100, "flexible_chapters": 15, "acts": [{"act_number": 1, "name": "开端", "description": "建立世界观和主角初始状态", "chapter_start": 1, "chapter_end": 30, "key_events": ["主角入门", "世界观展示", "初次冲突", "天赋觉醒"], "plot_units": [{"id": "723c910f-29b2-40c5-8fb3-be527b137fa3", "title": "世界观建立", "core_goal": "建立故事世界的基础设定和规则", "external_conflict": "主角初入陌生环境的适应困难", "internal_conflict": "对自身身份和能力的怀疑", "foreshadowing": [{"type": "道具", "content": "祖传玉佩的神秘光芒", "reveal_chapter": 40, "significance": "上古剑修身份的关键证据"}], "emotion_curve": ["困惑", "好奇", "警惕", "决心"], "trigger_events": ["进入宗门", "首次试炼", "发现异常"], "chapter_range": [1, 6]}, {"id": "bbdfa7f6-bb9e-4ad4-a8db-5b5d51776d03", "title": "主要角色登场", "core_goal": "介绍核心角色并建立初始关系", "external_conflict": "与同门师兄弟的竞争和冲突", "internal_conflict": "渴望被认可但害怕暴露秘密", "foreshadowing": [{"type": "对话", "content": "神秘师兄的意味深长话语", "reveal_chapter": 50, "significance": "暗示其真实身份为暗桩"}], "emotion_curve": ["孤独", "渴望", "紧张", "希望"], "trigger_events": ["结识同门", "师父指导", "初显天赋"], "chapter_range": [7, 30]}], "core_settings": ["修炼体系", "宗门规则", "祖传玉佩"]}, {"act_number": 2, "name": "发展", "description": "通过多次转折推进主线剧情", "chapter_start": 31, "chapter_end": 80, "key_events": ["转折点1", "转折点2", "转折点3"], "plot_units": [{"id": "52af7b43-8421-46af-bf49-f4118f067a6b", "title": "发展阶段1", "core_goal": "推进主线剧情，完成第1次重大转折", "external_conflict": "面临第1级别的外部威胁", "internal_conflict": "在第1次选择中的道德挣扎", "foreshadowing": [{"type": "场景", "content": "第1阶段的关键线索", "reveal_chapter": 56, "significance": "为后续剧情发展埋下伏笔"}], "emotion_curve": ["平静", "紧张", "冲突", "成长", "新的决心"], "trigger_events": ["新威胁出现", "能力提升", "关系变化"], "chapter_range": [31, 46]}, {"id": "b9226c69-6bfc-4b79-b1f9-ab608e8d686d", "title": "发展阶段2", "core_goal": "推进主线剧情，完成第2次重大转折", "external_conflict": "面临第2级别的外部威胁", "internal_conflict": "在第2次选择中的道德挣扎", "foreshadowing": [{"type": "场景", "content": "第2阶段的关键线索", "reveal_chapter": 72, "significance": "为后续剧情发展埋下伏笔"}], "emotion_curve": ["平静", "紧张", "冲突", "成长", "新的决心"], "trigger_events": ["新威胁出现", "能力提升", "关系变化"], "chapter_range": [47, 62]}, {"id": "799220f3-1e27-4985-9950-c41f431dcbb1", "title": "发展阶段3", "core_goal": "推进主线剧情，完成第3次重大转折", "external_conflict": "面临第3级别的外部威胁", "internal_conflict": "在第3次选择中的道德挣扎", "foreshadowing": [{"type": "场景", "content": "第3阶段的关键线索", "reveal_chapter": 88, "significance": "为后续剧情发展埋下伏笔"}], "emotion_curve": ["平静", "紧张", "冲突", "成长", "新的决心"], "trigger_events": ["新威胁出现", "能力提升", "关系变化"], "chapter_range": [63, 78]}], "core_settings": ["能力进阶", "敌对势力", "关键盟友"]}, {"act_number": 3, "name": "高潮/结局", "description": "解决核心冲突，完成主角蜕变", "chapter_start": 81, "chapter_end": 100, "key_events": ["真相大白", "最终决战", "主角蜕变", "新的开始"], "plot_units": [{"id": "ef639940-7e67-407d-8bb5-0fd509eea875", "title": "最终对决", "core_goal": "解决核心矛盾，完成主角蜕变", "external_conflict": "与最终反派的生死决战", "internal_conflict": "在力量与原则间的最终选择", "foreshadowing": [], "emotion_curve": ["紧张", "绝望", "觉悟", "爆发", "胜利"], "trigger_events": ["真相揭露", "最终觉醒", "决战开始", "胜负已分"], "chapter_range": [81, 97]}, {"id": "4a34f3b1-b5b4-4950-9869-920133934c40", "title": "尾声", "core_goal": "展示主角蜕变后的新状态", "external_conflict": "处理战后余波", "internal_conflict": "对未来道路的思考", "foreshadowing": [], "emotion_curve": ["平静", "反思", "希望"], "trigger_events": ["重建秩序", "告别过去", "展望未来"], "chapter_range": [98, 100]}], "core_settings": ["终极能力", "核心真相", "新的平衡"]}], "global_foreshadowing": [{"id": "51d1ad8a-f2b1-47d6-9e23-68586f93d685", "type": "身世之谜", "content": "主角真实身份的线索", "plant_chapters": [3, 8, 15, 25], "reveal_chapter": 80, "significance": "核心主题的关键支撑"}, {"id": "163153de-3018-4d2d-8f5d-30cb3b58c745", "type": "反派动机", "content": "反派行为背后的深层原因", "plant_chapters": [12, 22, 35, 50], "reveal_chapter": 85, "significance": "增加反派的复杂性和可信度"}], "created_at": "2025-06-08T15:00:17.523348", "updated_at": "2025-06-08T15:00:17.523348"}, "worldview": {"name": "多子多福系统世界观", "geography": [{"name": "苍澜大陆", "core_features": "东部为魔法生物聚居的雾隐森林，西部是机械侏儒建造的齿轮城邦", "special_rules": [{"rule": "雾隐森林内火系魔法威力下降30%", "effect": "声音传播距离缩短50%"}, {"rule": "齿轮城邦内机械装置效率提升20%", "effect": "魔法波动会干扰精密机械"}], "climate": "温带大陆性气候，四季分明", "inhabitants": ["人类", "精灵", "侏儒", "魔法生物"], "key_locations": ["剑宗", "雾隐森林", "齿轮城邦", "血河教总坛"]}, {"name": "熔火堡", "core_features": "建立在活火山口的炎裔独立聚落", "special_rules": [{"rule": "火系法术威力提升50%", "effect": "冰系法术威力下降70%"}, {"rule": "高温环境对非炎裔造成持续伤害", "effect": "炎裔在此地恢复速度加快"}], "climate": "极热，常年高温", "inhabitants": ["炎裔"], "key_locations": ["熔火宫", "炎龙祭坛", "岩浆矿场"]}], "power_systems": [{"name": "剑修体系", "levels": ["练气期", "筑基期", "金丹期", "元婴期", "化神期", "炼虚期", "合体期", "大乘期", "渡劫期"], "advancement_method": "通过魔兽山脉的「心魂试炼」，感悟剑意，凝聚剑魂", "special_abilities": [{"name": "剑气外放", "description": "将内力凝聚成剑气，远程攻击"}, {"name": "剑意共鸣", "description": "与天地剑意产生共鸣，威力倍增"}, {"name": "剑魂觉醒", "description": "觉醒前世剑修记忆和技能"}], "limitations": ["需要剑器作为媒介", "情绪波动会影响剑意稳定", "过度使用会损伤经脉"]}, {"name": "血河邪功", "levels": ["血气入门", "血煞小成", "血海中成", "血河大成", "血神化身"], "advancement_method": "吸收他人精血，炼化血煞之气", "special_abilities": [{"name": "血煞侵蚀", "description": "血气侵蚀敌人身体和神智"}, {"name": "血影分身", "description": "用血气凝聚分身"}], "limitations": ["需要不断杀戮补充血气", "容易被正道功法克制", "修炼过度会失去理智"]}], "social_structures": [{"name": "剑宗", "hierarchy": [{"level": "宗主", "description": "统领全宗，决定重大事务"}, {"level": "长老", "description": "管理各堂事务，指导弟子修炼"}, {"level": "内门弟子", "description": "核心弟子，享有特殊待遇"}, {"level": "外门弟子", "description": "普通弟子，需完成基础修炼"}, {"level": "杂役弟子", "description": "负责宗门日常事务"}], "governance": "长老会议制，重大决策需要三分之二长老同意", "laws_and_customs": ["不得残杀同门", "不得泄露宗门秘法", "每月需完成规定任务", "尊师重道，维护宗门声誉"], "conflicts": ["内门与外门弟子的资源争夺", "不同长老派系的权力斗争", "与血河教的世代仇恨"]}, {"name": "齿轮城邦", "hierarchy": [{"level": "机械议会", "description": "由七名大工匠组成的最高决策机构"}, {"level": "齿轮贵族", "description": "拥有大型工坊的富商家族"}, {"level": "工匠", "description": "掌握机械技术的技术人员"}, {"level": "平民", "description": "普通居民，需佩戴身份齿轮"}], "governance": "议会与贵族共同治理，重视技术创新", "laws_and_customs": ["所有居民必须佩戴身份齿轮", "技术创新者享有专利保护", "禁止在城内使用大型魔法", "每年举办机械竞技大会"], "conflicts": ["传统工艺与魔法技术的冲突", "贵族与平民的阶级矛盾", "资源分配不均引发的社会问题"]}], "history": [{"name": "炎魔之乱", "time_period": "百年前", "description": "上古炎魔苏醒，肆虐苍澜大陆，导致三分之一土地被熔岩覆盖", "impact": "催生了抗火种族「炎裔」，改变了大陆地理格局", "legacy_issues": ["炎裔因外貌特征被人类城邦驱逐", "熔岩地带成为禁区，隐藏着炎魔遗迹", "火系魔法变得更加强大但难以控制"], "related_characters": ["炎裔公主火舞", "上古剑修（主角前世）"]}, {"name": "剑宗建立", "time_period": "三百年前", "description": "上古剑修创立剑宗，传承剑道真谛", "impact": "成为大陆最强正道门派，维护正义秩序", "legacy_issues": ["剑宗秘法失传，只留下残缺功法", "与血河教结下世代仇恨", "内部派系斗争影响团结"], "related_characters": ["剑宗创始人（主角前世）", "血河教主（前世师弟）"]}], "magic_rules": {"基础规则": {"灵力来源": "天地灵气，通过修炼吸收转化", "施法媒介": "需要相应的法器或手势咒语", "消耗机制": "消耗修炼者的内力和精神力", "冷却时间": "强大法术需要恢复时间"}, "元素相克": {"火克冰": "火系法术对冰系造成额外伤害", "水克火": "水系法术可以熄灭火焰", "土克水": "土系法术可以吸收水分", "风助火": "风系法术可以增强火系威力"}, "环境影响": {"雾隐森林": "火系威力-30%，声音传播-50%", "熔火堡": "火系威力+50%，冰系威力-70%", "齿轮城邦": "魔法波动干扰精密机械"}, "禁忌法术": {"血祭术": "以生命为代价的邪恶法术", "灵魂操控": "控制他人意识的禁术", "时空扭曲": "改变时间流速的危险法术"}}, "technology_level": "古代修仙", "created_at": "2025-06-08T15:00:17.525139", "updated_at": "2025-06-08T15:00:17.525139"}, "characters": {"5ff71162-d4c1-4486-a748-369d4752f526": {"id": "5ff71162-d4c1-4486-a748-369d4752f526", "basic_info": {"full_name": "林砚冰", "nickname": "小冰", "age": 18, "appearance": "清秀少年，身材偏瘦，黑发黑眸", "distinctive_features": "左眼角有泪痣，总穿绣着银线鸢尾的青衫", "profession": "剑宗外门弟子", "hidden_identity": "上古剑修残魂宿主"}, "motivation": {"core_desire": "证明自己不是「灾星转世」，获得母亲的认可", "secondary_goals": ["三个月内进入剑宗内门试炼前10名", "掌握祖传剑法的真正奥义", "找到母亲失踪的真相"], "fears": [{"trigger": "听到锁链断裂声", "reaction": "引发panic发作（童年被囚禁记忆）"}, {"trigger": "被人称为灾星", "reaction": "情绪失控，剑意不稳"}], "obsessions": [{"item": "母亲留下的断剑穗", "meaning": "坚信集齐七段可复活母亲"}]}, "behavior": {"personality_triggers": [{"trigger": "和你父亲一样是个废物", "state": "暴怒", "behavior": "瞳孔变红，握剑的手青筋暴起，语言风格从温和转为冷硬"}, {"trigger": "提及母亲", "state": "悲伤", "behavior": "下意识摸向胸前的断剑穗，声音变得轻柔"}], "habits": {"actions": ["思考时会摩挲剑柄上的云纹雕刻", "紧张时会整理衣袖", "习惯在清晨练剑"], "speech": ["常用反问句「难道不是这样吗？」", "紧张时会重复尾字", "对长辈使用敬语"]}, "value_conflicts": [{"scenario": "同伴要求屠杀被控制的平民", "conflict": "在「保护同伴」与「坚守不杀原则」间挣扎"}]}, "relationships": {"52c85cdf-e141-4fc3-8490-f8e0cc98642e": {"name": "楚离", "surface_relation": "师兄弟", "hidden_relation": "保护者", "trust_level": 60, "key_events": []}}, "development_arc": [{"stage": "初期", "chapters": [1, 30], "description": "自卑迷茫的废柴弟子", "key_changes": ["发现天赋", "建立信心"], "emotional_state": "迷茫→希望"}, {"stage": "中期", "chapters": [31, 70], "description": "逐渐成长的剑修", "key_changes": ["掌握力量", "面对真相"], "emotional_state": "希望→冲突"}, {"stage": "后期", "chapters": [71, 100], "description": "觉醒的上古剑修", "key_changes": ["接受身份", "完成蜕变"], "emotional_state": "冲突→成熟"}], "current_state": {"cultivation_level": "练气三层", "emotional_state": "迷茫但坚定", "key_items": ["祖传玉佩", "断剑穗"], "known_secrets": [], "loyalty_values": {}}, "created_at": "2025-06-08T15:00:17.527132", "updated_at": "2025-06-08T15:00:17.527132"}, "52c85cdf-e141-4fc3-8490-f8e0cc98642e": {"id": "52c85cdf-e141-4fc3-8490-f8e0cc98642e", "basic_info": {"full_name": "楚离", "nickname": "离师兄", "age": 22, "appearance": "俊美青年，剑眉星目，气质冷峻", "distinctive_features": "右手腕有一道细长疤痕，喜穿黑色劲装", "profession": "剑宗内门弟子", "hidden_identity": "剑宗暗桩，奉命保护主角"}, "motivation": {"core_desire": "完成师父的遗愿，保护林砚冰", "secondary_goals": ["隐藏真实身份", "调查血河教的阴谋", "提升自身实力"], "fears": [{"trigger": "身份暴露", "reaction": "变得更加警惕和冷漠"}], "obsessions": [{"item": "师父的遗书", "meaning": "唯一的行动指南"}]}, "behavior": {"personality_triggers": [{"trigger": "有人威胁林砚冰", "state": "保护模式", "behavior": "立即出手，不计后果"}], "habits": {"actions": ["习惯观察周围环境", "喜欢站在阴影中"], "speech": ["言简意赅", "很少主动开口"]}, "value_conflicts": [{"scenario": "保护主角与完成任务冲突", "conflict": "在忠诚与理智间选择"}]}, "relationships": {}, "development_arc": [], "current_state": {"cultivation_level": "筑基后期", "emotional_state": "冷静克制", "key_items": ["师父遗书", "暗桩令牌"], "known_secrets": ["主角真实身份", "血河教计划"], "loyalty_values": {"林砚冰": 90, "剑宗": 70}}, "created_at": "2025-06-08T15:00:17.527132", "updated_at": "2025-06-08T15:00:17.527132"}, "2bef432c-3600-4529-8b6c-f1eb5505ef97": {"id": "2bef432c-3600-4529-8b6c-f1eb5505ef97", "basic_info": {"full_name": "叶默生", "nickname": "叶长老", "age": 45, "appearance": "中年男子，须发半白，眼神深邃", "distinctive_features": "左手缺少小指，胸前有剑伤疤痕", "profession": "剑宗长老", "hidden_identity": "林砚冰的生父"}, "motivation": {"core_desire": "", "secondary_goals": [], "fears": [], "obsessions": []}, "behavior": {"personality_triggers": [], "habits": {}, "value_conflicts": []}, "relationships": {}, "development_arc": [], "current_state": {}, "created_at": "2025-06-08T15:00:17.527132", "updated_at": "2025-06-08T15:00:17.527132"}, "16e667cf-f09f-4f56-bc8b-4f0cecebcbaa": {"id": "16e667cf-f09f-4f56-bc8b-4f0cecebcbaa", "basic_info": {"full_name": "血河教主", "nickname": "血主", "age": 60, "appearance": "阴鸷老者，血红长袍，气息邪恶", "distinctive_features": "双眼血红，指甲如刀", "profession": "血河教教主", "hidden_identity": "林砚冰前世的师弟"}, "motivation": {"core_desire": "", "secondary_goals": [], "fears": [], "obsessions": []}, "behavior": {"personality_triggers": [], "habits": {}, "value_conflicts": []}, "relationships": {}, "development_arc": [], "current_state": {}, "created_at": "2025-06-08T15:00:17.527132", "updated_at": "2025-06-08T15:00:17.527132"}, "bdb8cc2a-9038-4b8f-b67d-0d63ed7a398c": {"id": "bdb8cc2a-9038-4b8f-b67d-0d63ed7a398c", "basic_info": {"full_name": "火舞", "nickname": "舞儿", "age": 17, "appearance": "炎裔少女，火红长发，皮肤微红", "distinctive_features": "额头有火焰印记，体温偏高", "profession": "炎裔战士", "hidden_identity": "炎裔公主"}, "motivation": {"core_desire": "", "secondary_goals": [], "fears": [], "obsessions": []}, "behavior": {"personality_triggers": [], "habits": {}, "value_conflicts": []}, "relationships": {}, "development_arc": [], "current_state": {}, "created_at": "2025-06-08T15:00:17.527132", "updated_at": "2025-06-08T15:00:17.527132"}}, "config": {"title": "多子多福系统", "genre": "都市系统", "theme": "家庭责任与个人成长的平衡", "main_plot": "普通上班族林浩意外获得多子多福系统，每生一个孩子就能获得丰厚奖励，从此走上人生巅峰的故事", "total_chapters": 100, "target_words_per_chapter": 3000, "style": "轻松幽默", "target_audience": "都市小说读者", "protagonist": {"name": "林浩", "age": 28, "occupation": "普通程序员", "personality": "善良、负责任、有些内向但很有爱心", "background": "普通工薪家庭出身，大学毕业后在一家互联网公司工作", "initial_state": "单身，月薪8000，租房住，生活平淡"}, "heroine": {"name": "苏晴雨", "age": 26, "occupation": "小学教师", "personality": "温柔、善良、喜欢孩子、有教育理想", "background": "师范大学毕业，在市区小学任教", "relationship": "林浩的大学同学，暗恋多年"}, "system_settings": {"name": "多子多福系统", "core_function": "每生育一个孩子获得丰厚奖励", "reward_types": ["金钱奖励（每个孩子100万-1000万不等）", "技能提升（育儿技能、工作技能等）", "身体强化（体质、智力、魅力提升）", "特殊道具（育儿神器、学习辅助等）", "房产车产（豪宅、豪车等）", "投资机会（股票内幕、创业项目等）"], "upgrade_conditions": "孩子数量、教育成果、家庭和谐度", "special_features": ["孕期保护（确保母子平安）", "教育辅助（提供最佳教育方案）", "健康监测（实时监控家人健康）", "财富管理（投资理财建议）"]}, "main_plotlines": ["系统觉醒：林浩意外获得系统", "初次奖励：与苏晴雨结婚生子获得第一笔奖励", "事业起步：利用系统奖励开始创业", "家庭扩大：陆续生育更多孩子，获得更多奖励", "社会影响：成为成功企业家和模范父亲", "系统升级：解锁更高级功能", "挑战应对：面对各种家庭和事业挑战", "终极目标：建立幸福大家庭，实现人生价值"], "supporting_characters": [{"name": "林建国", "role": "林浩父亲", "personality": "传统、重视家庭、希望儿子成家立业"}, {"name": "王美华", "role": "林浩母亲", "personality": "慈祥、爱操心、特别喜欢孙子孙女"}, {"name": "苏父苏母", "role": "苏晴雨父母", "personality": "知识分子家庭，重视教育"}, {"name": "张伟", "role": "林浩好友", "personality": "单身贵族，对林浩的变化感到惊讶"}], "special_requirements": ["正能量导向，强调家庭责任和爱", "避免过度物质化，注重精神层面成长", "合理安排生育节奏，符合现实逻辑", "展现现代育儿理念和教育方式", "平衡事业与家庭的关系", "体现社会责任感"], "first_five_chapters": [{"chapter": 1, "title": "平凡的程序员", "main_events": ["介绍林浩的平凡生活", "公司加班，生活压力", "偶遇大学同学苏晴雨", "内心的孤独和对未来的迷茫"], "key_elements": ["建立主角形象", "展现现实生活压力", "埋下感情线伏笔"]}, {"chapter": 2, "title": "神秘系统降临", "main_events": ["深夜加班回家路上", "意外触发多子多福系统", "系统功能介绍", "林浩的震惊和怀疑"], "key_elements": ["系统正式登场", "建立核心设定", "主角心理变化"]}, {"chapter": 3, "title": "重逢的机会", "main_events": ["主动联系苏晴雨", "约会看电影吃饭", "回忆大学时光", "感情升温"], "key_elements": ["推进感情线", "展现主角变化", "为后续发展铺垫"]}, {"chapter": 4, "title": "告白与承诺", "main_events": ["林浩鼓起勇气告白", "苏晴雨的惊喜和感动", "确立恋爱关系", "对未来的憧憬"], "key_elements": ["感情线重大进展", "为结婚生子做准备", "展现主角成长"]}, {"chapter": 5, "title": "求婚与系统奖励", "main_events": ["精心准备的求婚", "苏晴雨答应求婚", "系统发布结婚任务", "获得第一次系统奖励"], "key_elements": ["重要情节节点", "系统奖励机制展现", "为后续发展奠定基础"]}]}}