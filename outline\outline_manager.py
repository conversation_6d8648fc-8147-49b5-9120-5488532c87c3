#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大纲管理器 - 构建动态故事坐标系
"""

import json
import uuid
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from utils.logger import LoggerMixin

@dataclass
class PlotUnit:
    """情节单元"""
    id: str
    title: str
    core_goal: str
    external_conflict: str
    internal_conflict: str
    foreshadowing: List[Dict[str, Any]]
    emotion_curve: List[str]
    trigger_events: List[str]
    chapter_range: List[int]

@dataclass
class ActStructure:
    """幕结构"""
    act_number: int
    name: str
    description: str
    chapter_start: int
    chapter_end: int
    key_events: List[str]
    plot_units: List[PlotUnit]
    core_settings: List[str]  # 核心设定植入

@dataclass
class Outline:
    """大纲结构"""
    title: str
    main_plot: str
    theme_core: str
    total_chapters: int
    flexible_chapters: int
    acts: List[ActStructure]
    global_foreshadowing: List[Dict[str, Any]]
    created_at: str
    updated_at: str

class OutlineManager(LoggerMixin):
    """大纲管理器"""
    
    def __init__(self, config):
        self.config = config
        self.outline_config = config.get_nested_config('system', 'outline', default={})
    
    def create_outline(self, novel_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建结构化大纲"""
        self.logger.info("开始创建结构化大纲")
        
        # 基础信息
        title = novel_config['title']
        main_plot = novel_config['main_plot']
        theme_core = novel_config['theme']
        total_chapters = novel_config['total_chapters']
        
        # 计算弹性章节数
        flexible_ratio = self.outline_config.get('default_structure', {}).get('flexible_chapters_ratio', 0.15)
        flexible_chapters = int(total_chapters * flexible_ratio)
        
        # 创建三幕结构
        acts = self._create_three_act_structure(total_chapters, novel_config)
        
        # 创建全局伏笔
        global_foreshadowing = self._create_global_foreshadowing(novel_config)
        
        outline = Outline(
            title=title,
            main_plot=main_plot,
            theme_core=theme_core,
            total_chapters=total_chapters,
            flexible_chapters=flexible_chapters,
            acts=acts,
            global_foreshadowing=global_foreshadowing,
            created_at=self._get_timestamp(),
            updated_at=self._get_timestamp()
        )
        
        self.logger.info(f"大纲创建完成，共{len(acts)}幕，{total_chapters}章")
        return asdict(outline)
    
    def _create_three_act_structure(self, total_chapters: int, novel_config: Dict) -> List[ActStructure]:
        """创建三幕式结构"""
        default_ratios = self.outline_config.get('default_structure', {}).get('chapters_per_act', [30, 50, 20])
        
        # 计算各幕章节数
        act1_chapters = int(total_chapters * default_ratios[0] / 100)
        act3_chapters = int(total_chapters * default_ratios[2] / 100)
        act2_chapters = total_chapters - act1_chapters - act3_chapters
        
        acts = []
        
        # 第一幕：开端
        act1 = self._create_act_one(1, act1_chapters, novel_config)
        acts.append(act1)
        
        # 第二幕：发展
        act2 = self._create_act_two(act1_chapters + 1, act1_chapters + act2_chapters, novel_config)
        acts.append(act2)
        
        # 第三幕：高潮/结局
        act3 = self._create_act_three(act1_chapters + act2_chapters + 1, total_chapters, novel_config)
        acts.append(act3)
        
        return acts
    
    def _create_act_one(self, start_chapter: int, end_chapter: int, novel_config: Dict) -> ActStructure:
        """创建第一幕：开端"""
        plot_units = []
        
        # 世界观建立单元
        worldview_unit = PlotUnit(
            id=str(uuid.uuid4()),
            title="世界观建立",
            core_goal="建立故事世界的基础设定和规则",
            external_conflict="主角初入陌生环境的适应困难",
            internal_conflict="对自身身份和能力的怀疑",
            foreshadowing=[
                {
                    "type": "道具",
                    "content": "祖传玉佩的神秘光芒",
                    "reveal_chapter": end_chapter + 10,
                    "significance": "上古剑修身份的关键证据"
                }
            ],
            emotion_curve=["困惑", "好奇", "警惕", "决心"],
            trigger_events=["进入宗门", "首次试炼", "发现异常"],
            chapter_range=[start_chapter, start_chapter + 5]
        )
        plot_units.append(worldview_unit)
        
        # 角色介绍单元
        character_unit = PlotUnit(
            id=str(uuid.uuid4()),
            title="主要角色登场",
            core_goal="介绍核心角色并建立初始关系",
            external_conflict="与同门师兄弟的竞争和冲突",
            internal_conflict="渴望被认可但害怕暴露秘密",
            foreshadowing=[
                {
                    "type": "对话",
                    "content": "神秘师兄的意味深长话语",
                    "reveal_chapter": end_chapter + 20,
                    "significance": "暗示其真实身份为暗桩"
                }
            ],
            emotion_curve=["孤独", "渴望", "紧张", "希望"],
            trigger_events=["结识同门", "师父指导", "初显天赋"],
            chapter_range=[start_chapter + 6, end_chapter]
        )
        plot_units.append(character_unit)
        
        return ActStructure(
            act_number=1,
            name="开端",
            description="建立世界观和主角初始状态",
            chapter_start=start_chapter,
            chapter_end=end_chapter,
            key_events=["主角入门", "世界观展示", "初次冲突", "天赋觉醒"],
            plot_units=plot_units,
            core_settings=["修炼体系", "宗门规则", "祖传玉佩"]
        )
    
    def _create_act_two(self, start_chapter: int, end_chapter: int, novel_config: Dict) -> ActStructure:
        """创建第二幕：发展"""
        plot_units = []
        
        # 计算转折点数量
        total_chapters = end_chapter - start_chapter + 1
        turning_points = max(3, total_chapters // 15)  # 每15章一个转折点
        
        chapters_per_unit = total_chapters // turning_points
        
        for i in range(turning_points):
            unit_start = start_chapter + i * chapters_per_unit
            unit_end = min(start_chapter + (i + 1) * chapters_per_unit - 1, end_chapter)
            
            plot_unit = PlotUnit(
                id=str(uuid.uuid4()),
                title=f"发展阶段{i+1}",
                core_goal=f"推进主线剧情，完成第{i+1}次重大转折",
                external_conflict=f"面临第{i+1}级别的外部威胁",
                internal_conflict=f"在第{i+1}次选择中的道德挣扎",
                foreshadowing=[
                    {
                        "type": "场景",
                        "content": f"第{i+1}阶段的关键线索",
                        "reveal_chapter": unit_end + 10,
                        "significance": f"为后续剧情发展埋下伏笔"
                    }
                ],
                emotion_curve=["平静", "紧张", "冲突", "成长", "新的决心"],
                trigger_events=[f"新威胁出现", f"能力提升", f"关系变化"],
                chapter_range=[unit_start, unit_end]
            )
            plot_units.append(plot_unit)
        
        return ActStructure(
            act_number=2,
            name="发展",
            description="通过多次转折推进主线剧情",
            chapter_start=start_chapter,
            chapter_end=end_chapter,
            key_events=[f"转折点{i+1}" for i in range(turning_points)],
            plot_units=plot_units,
            core_settings=["能力进阶", "敌对势力", "关键盟友"]
        )
    
    def _create_act_three(self, start_chapter: int, end_chapter: int, novel_config: Dict) -> ActStructure:
        """创建第三幕：高潮/结局"""
        plot_units = []
        
        # 高潮单元
        climax_unit = PlotUnit(
            id=str(uuid.uuid4()),
            title="最终对决",
            core_goal="解决核心矛盾，完成主角蜕变",
            external_conflict="与最终反派的生死决战",
            internal_conflict="在力量与原则间的最终选择",
            foreshadowing=[],  # 高潮阶段主要是回收伏笔
            emotion_curve=["紧张", "绝望", "觉悟", "爆发", "胜利"],
            trigger_events=["真相揭露", "最终觉醒", "决战开始", "胜负已分"],
            chapter_range=[start_chapter, end_chapter - 3]
        )
        plot_units.append(climax_unit)
        
        # 结局单元
        ending_unit = PlotUnit(
            id=str(uuid.uuid4()),
            title="尾声",
            core_goal="展示主角蜕变后的新状态",
            external_conflict="处理战后余波",
            internal_conflict="对未来道路的思考",
            foreshadowing=[],
            emotion_curve=["平静", "反思", "希望"],
            trigger_events=["重建秩序", "告别过去", "展望未来"],
            chapter_range=[end_chapter - 2, end_chapter]
        )
        plot_units.append(ending_unit)
        
        return ActStructure(
            act_number=3,
            name="高潮/结局",
            description="解决核心冲突，完成主角蜕变",
            chapter_start=start_chapter,
            chapter_end=end_chapter,
            key_events=["真相大白", "最终决战", "主角蜕变", "新的开始"],
            plot_units=plot_units,
            core_settings=["终极能力", "核心真相", "新的平衡"]
        )
    
    def _create_global_foreshadowing(self, novel_config: Dict) -> List[Dict[str, Any]]:
        """创建全局伏笔"""
        return [
            {
                "id": str(uuid.uuid4()),
                "type": "身世之谜",
                "content": "主角真实身份的线索",
                "plant_chapters": [3, 8, 15, 25],
                "reveal_chapter": 80,
                "significance": "核心主题的关键支撑"
            },
            {
                "id": str(uuid.uuid4()),
                "type": "反派动机",
                "content": "反派行为背后的深层原因",
                "plant_chapters": [12, 22, 35, 50],
                "reveal_chapter": 85,
                "significance": "增加反派的复杂性和可信度"
            }
        ]
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_chapter_outline(self, outline: Dict, chapter_num: int) -> Dict[str, Any]:
        """获取指定章节的大纲信息"""
        for act in outline['acts']:
            if act['chapter_start'] <= chapter_num <= act['chapter_end']:
                for plot_unit in act['plot_units']:
                    if plot_unit['chapter_range'][0] <= chapter_num <= plot_unit['chapter_range'][1]:
                        return {
                            'act': act,
                            'plot_unit': plot_unit,
                            'chapter_position': chapter_num - plot_unit['chapter_range'][0] + 1,
                            'unit_total_chapters': plot_unit['chapter_range'][1] - plot_unit['chapter_range'][0] + 1
                        }
        return {}
    
    def update_outline(self, outline: Dict, updates: Dict) -> Dict:
        """更新大纲"""
        outline.update(updates)
        outline['updated_at'] = self._get_timestamp()
        return outline
