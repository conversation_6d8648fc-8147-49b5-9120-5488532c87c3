#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CherryStudio兼容的API调用
"""

import requests
import json

def test_cherry_api():
    """测试CherryStudio API"""
    base_url = "https://lazy-bear-13.deno.dev"
    api_key = "awdsadwasdas"
    
    # 尝试不同的模型名称
    models_to_test = [
        "claude-3-7-sonnet",
        "claude-3-sonnet",
        "claude-3-sonnet-20240229",
        "gemini-2.5-flash",
        "gpt-4.1-mini",
        "qwen-qwq"
    ]
    
    for model in models_to_test:
        print(f"\n{'='*50}")
        print(f"测试模型: {model}")
        print(f"{'='*50}")
        
        # OpenAI兼容格式
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": "你好，请简单介绍一下你自己"}
            ],
            "max_tokens": 200,
            "temperature": 0.7,
            "stream": False
        }
        
        try:
            # 尝试 /v1/chat/completions 端点
            response = requests.post(
                f"{base_url}/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查usage
                usage = result.get('usage', {})
                print(f"Token使用: {usage}")
                
                # 检查内容
                choices = result.get('choices', [])
                if choices and len(choices) > 0:
                    message = choices[0].get('message', {})
                    content = message.get('content', '')
                    
                    print(f"内容长度: {len(content)}")
                    print(f"内容: {content[:200]}...")
                    
                    if content.strip():
                        print("✅ 成功获取内容!")
                        return model, data  # 返回成功的配置
                    else:
                        print("❌ 内容为空")
                else:
                    print("❌ 没有choices或choices为空")
                    print(f"完整响应: {json.dumps(result, indent=2, ensure_ascii=False)[:500]}...")
            else:
                print(f"❌ 请求失败: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
    
    return None, None

def test_specific_model():
    """测试特定模型配置"""
    base_url = "https://lazy-bear-13.deno.dev"
    api_key = "awdsadwasdas"
    
    print("\n" + "="*50)
    print("测试claude-3-7-sonnet模型")
    print("="*50)
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    # 尝试更简单的请求
    data = {
        "model": "claude-3-7-sonnet",
        "messages": [
            {"role": "user", "content": "写一个关于程序员的简短故事，50字左右"}
        ],
        "max_tokens": 100
    }
    
    print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            f"{base_url}/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            choices = result.get('choices', [])
            if choices:
                content = choices[0].get('message', {}).get('content', '')
                if content:
                    print(f"\n✅ 成功获取内容:")
                    print(f"'{content}'")
                    return True
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"异常: {str(e)}")
    
    return False

if __name__ == "__main__":
    print("开始测试CherryStudio兼容API...")
    
    # 先测试特定模型
    if test_specific_model():
        print("\n🎉 API调用成功！")
    else:
        print("\n继续测试其他模型...")
        success_model, success_config = test_cherry_api()
        
        if success_model:
            print(f"\n🎉 找到可用模型: {success_model}")
            print("建议更新配置使用此模型")
        else:
            print("\n❌ 所有模型测试都失败了")
            print("建议检查:")
            print("1. API密钥是否正确")
            print("2. 网络连接是否正常") 
            print("3. API服务是否可用")
