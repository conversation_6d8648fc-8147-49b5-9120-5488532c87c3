#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试集成的API是否正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from ai_interface.ai_client import AI<PERSON>lient
from utils.config_loader import Config<PERSON>oa<PERSON>

def test_api_integration():
    """测试API集成"""
    print("🔍 测试AI小说创作系统的API集成...")
    print("=" * 50)
    
    try:
        # 加载配置
        config = ConfigLoader()
        ai_config = config.get_ai_config()
        
        print(f"✅ 配置加载成功")
        print(f"   主要模型: {ai_config['ai_models']['primary']['model']}")
        print(f"   API地址: {ai_config['ai_models']['primary']['base_url']}")
        print()
        
        # 初始化AI客户端
        client = AIClient(ai_config)
        
        # 测试1：简单连接测试
        print("📝 测试1: 简单连接测试")
        test_prompt = "请回复：API连接成功"
        result = client.generate_text(test_prompt)
        
        if result and "API连接成功" in result:
            print(f"✅ 连接测试通过")
            print(f"   响应: {result}")
        else:
            print(f"❌ 连接测试失败")
            print(f"   响应: {result}")
            return False
        
        print()
        
        # 测试2：小说创作测试
        print("📝 测试2: 小说创作能力测试")
        story_prompt = """请创作一个简短的小说开头，要求：
- 主角：程序员林浩
- 场景：深夜的办公室
- 情节：加班时发现了一个神秘的系统
- 字数：200字左右

请开始创作："""
        
        story_result = client.generate_text(story_prompt)
        
        if story_result and len(story_result) > 100:
            print(f"✅ 小说创作测试通过")
            print(f"   生成字数: {len(story_result)}")
            print(f"   内容预览: {story_result[:100]}...")
        else:
            print(f"❌ 小说创作测试失败")
            print(f"   响应: {story_result}")
            return False
        
        print()
        
        # 测试3：系统提示词测试
        print("📝 测试3: 系统提示词测试")
        system_prompt = "你是一个专业的小说创作AI助手，擅长创作都市系统文。"
        user_prompt = "请为小说《多子多福系统》创作第一章的标题和简介。"
        
        system_result = client.generate_text(user_prompt, system_prompt)
        
        if system_result and len(system_result) > 50:
            print(f"✅ 系统提示词测试通过")
            print(f"   生成内容: {system_result}")
        else:
            print(f"❌ 系统提示词测试失败")
            print(f"   响应: {system_result}")
            return False
        
        print()
        print("🎉 所有API集成测试通过！")
        print("系统已准备好进行小说创作。")
        return True
        
    except Exception as e:
        print(f"❌ API集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_novel_system():
    """测试完整的小说创作系统"""
    print("\n" + "=" * 50)
    print("🎯 测试完整小说创作系统")
    print("=" * 50)
    
    try:
        from main import NovelWritingSystem
        
        # 创建系统实例
        system = NovelWritingSystem()
        print("✅ 小说创作系统初始化成功")
        
        # 测试配置
        novel_config = {
            "title": "测试小说",
            "genre": "都市系统",
            "theme": "成长与奋斗",
            "main_plot": "普通人获得系统后的成长故事",
            "protagonist": {
                "name": "林浩",
                "age": 28,
                "occupation": "程序员",
                "personality": "善良、负责任、内向"
            },
            "total_chapters": 5,
            "target_words_per_chapter": 2500
        }
        
        # 创建新项目
        project_dir = system.create_new_novel(novel_config)
        print(f"✅ 测试项目创建成功: {project_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 小说系统测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始API集成测试...")
    
    # 测试API集成
    api_success = test_api_integration()
    
    if api_success:
        # 测试完整系统
        system_success = test_novel_system()
        
        if system_success:
            print("\n🎊 恭喜！您的AI小说创作系统已完全就绪！")
            print("现在可以使用以下命令启动系统：")
            print("   python start.py")
            print("   或")
            print("   python main.py")
        else:
            print("\n⚠️ API集成正常，但系统测试有问题")
    else:
        print("\n😔 API集成测试失败，请检查配置")
        print("建议检查：")
        print("1. API密钥是否正确")
        print("2. 网络连接是否正常")
        print("3. API服务是否可用")
