import requests
import json
response = requests.post(
  url="https://openrouter.ai/api/v1/chat/completions",
  headers={
    "Authorization": "Bearer 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",# 改为你的API key,Bearer空格要带上
    "HTTP-Referer": "http://localhost:8088", # Optional. Site URL for rankings on openrouter.ai.
    "X-Title": "test", # Optional. Site title for rankings on openrouter.ai.
  },
  data=json.dumps({
        "model": "deepseek/deepseek-r1-0528:free", # 这个模型你可以在openrouter上的网站找到各种各样的
    "messages": [
      {
        "role": "user",
        "content": "中南大学在世界上排名怎么样，用中文回答"
      }
    ]
  })
)
print(response.json()['choices'][0]['message']['content'])

