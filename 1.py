import requests
import json

response = requests.post(
  url="https://openrouter.ai/api/v1/chat/completions",
  headers={
    "Authorization": "Bearer 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",
    "Content-Type": "application/json",
    "HTTP-Referer": "http://localhost:8088", # Optional. Site URL for rankings on openrouter.ai.
    "X-Title": "test", # Optional. Site title for rankings on openrouter.ai.
  },
  data=json.dumps({
    "model": "deepseek/deepseek-r1-0528:free",
    "messages": [
      {
        "role": "user",
        "content": "What is the meaning of life?"
      }
    ],

  })
)

# 打印响应
print(f"状态码: {response.status_code}")
print(f"响应头: {dict(response.headers)}")

if response.status_code == 200:
    result = response.json()
    print(f"完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

    # 提取回答内容
    if 'choices' in result and len(result['choices']) > 0:
        content = result['choices'][0]['message']['content']
        print(f"\n回答内容:\n{content}")
    else:
        print("没有找到回答内容")
else:
    print(f"请求失败: {response.text}")