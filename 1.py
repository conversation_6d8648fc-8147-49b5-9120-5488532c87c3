import requests
import json
response = requests.post(
  url="https://lazy-bear-13.deno.dev/v1/chat/completions",
  headers={
    "Authorization": "Bearer awdsadwasd",# 改为你的API key,Bearer空格要带上
  },
  data=json.dumps({
        "model": "claude-3-7-sonnet", # 这个模型你可以在openrouter上的网站找到各种各样的
    "messages": [
      {
        "role": "user",
        "content": "中南大学在世界上排名怎么样，用中文回答"
      }
    ]
  })
)
print(response.json()['choices'][0]['message']['content'])

