#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版多子多福系统文创建器
"""

import requests
import json
from pathlib import Path
from datetime import datetime

def call_api_direct(prompt):
    """直接调用API"""
    url = "https://lazy-bear-13.deno.dev/v1/chat/completions"
    
    headers = {
        'Authorization': 'Bearer awdsadwasd',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive'
    }
    
    data = {
        "model": "claude-3-7-sonnet",
        "messages": [
            {"role": "user", "content": prompt}
        ]
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            return content.strip()
        else:
            print(f"API错误: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"请求异常: {str(e)}")
        return None

def create_duozi_novel():
    """创建多子多福系统文"""
    print("🎉 创建多子多福系统文小说")
    print("=" * 50)
    
    # 测试API连接
    print("🔍 测试API连接...")
    test_result = call_api_direct("请回复：连接成功")
    if not test_result or "连接成功" not in test_result:
        print("❌ API连接失败")
        return
    
    print("✅ API连接成功")
    print()
    
    # 创建项目目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    project_name = f"多子多福系统_{timestamp}"
    project_dir = Path("data/projects") / project_name
    project_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 项目目录：{project_dir}")
    print()
    
    # 生成第一章
    print("📝 生成第一章...")
    
    chapter_prompt = """请创作小说《多子多福系统》的第一章，要求：

【小说设定】
- 类型：都市系统文
- 主角：林浩，28岁程序员，善良负责任但内向，月薪8000，单身
- 背景：现代都市，主角即将获得多子多福系统

【第一章要求】
- 标题：平凡的程序员
- 展现林浩的平凡生活和工作状态
- 描写深夜加班的真实场景
- 体现都市打工人的孤独和压力
- 为后续系统出现做铺垫
- 字数：2500-3500字
- 风格：真实自然，有代入感

【创作要求】
请创作完整的第一章内容，包括：
1. 生动的场景描写
2. 自然的内心独白
3. 真实的生活细节
4. 为后续剧情的铺垫

请开始创作："""

    content = call_api_direct(chapter_prompt)
    
    if content and len(content) > 1000:
        print("✅ 第一章生成成功！")
        print(f"   字数：{len(content)}")
        print()
        
        # 保存章节
        chapter_file = project_dir / "第一章_平凡的程序员.txt"
        with open(chapter_file, 'w', encoding='utf-8') as f:
            f.write("《多子多福系统》\n")
            f.write("第一章 平凡的程序员\n\n")
            f.write(content)
        
        # 保存项目信息
        project_info = {
            "title": "多子多福系统",
            "genre": "都市系统文",
            "protagonist": "林浩",
            "created_at": datetime.now().isoformat(),
            "chapter_1": {
                "title": "平凡的程序员",
                "word_count": len(content),
                "file": "第一章_平凡的程序员.txt"
            }
        }
        
        info_file = project_dir / "project_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(project_info, f, ensure_ascii=False, indent=2)
        
        print("📖 第一章内容：")
        print("=" * 50)
        print("第一章 平凡的程序员")
        print()
        print(content)
        print("=" * 50)
        
        print()
        print("📁 文件保存位置：")
        print(f"   项目目录：{project_dir}")
        print(f"   第一章文件：{chapter_file}")
        print(f"   项目信息：{info_file}")
        
        return True
        
    else:
        print("❌ 第一章生成失败")
        return False

if __name__ == "__main__":
    success = create_duozi_novel()
    
    if success:
        print()
        print("🎊 多子多福系统文第一章创建完成！")
        print("这是由AI创作的原创内容，展现了主角林浩的平凡生活。")
    else:
        print()
        print("😔 创建失败，请稍后重试。")
