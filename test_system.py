#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本 - 测试AI小说创作系统的各个模块
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from utils.config_loader import ConfigLoader
from outline.outline_manager import OutlineManager
from characters.character_manager import CharacterManager
from worldview.worldview_manager import WorldviewManager
from quality.quality_controller import QualityController
from example_config import QUICK_START_CONFIG

def test_config_loader():
    """测试配置加载器"""
    print("=== 测试配置加载器 ===")
    try:
        config = ConfigLoader()
        ai_config = config.get_ai_config()
        system_config = config.get_system_config()
        
        print(f"✓ 配置加载成功")
        print(f"  AI配置项数量: {len(ai_config)}")
        print(f"  系统配置项数量: {len(system_config)}")
        return True
    except Exception as e:
        print(f"✗ 配置加载失败: {str(e)}")
        return False

def test_outline_manager():
    """测试大纲管理器"""
    print("\n=== 测试大纲管理器 ===")
    try:
        config = ConfigLoader()
        outline_manager = OutlineManager(config)
        
        # 创建测试大纲
        outline = outline_manager.create_outline(QUICK_START_CONFIG)
        
        print(f"✓ 大纲创建成功")
        print(f"  小说标题: {outline['title']}")
        print(f"  总章节数: {outline['total_chapters']}")
        print(f"  幕数: {len(outline['acts'])}")
        
        # 测试获取章节大纲
        chapter_info = outline_manager.get_chapter_outline(outline, 1)
        if chapter_info:
            print(f"  第1章所属幕: 第{chapter_info['act']['act_number']}幕")
        
        return True
    except Exception as e:
        print(f"✗ 大纲管理器测试失败: {str(e)}")
        return False

def test_character_manager():
    """测试人物卡管理器"""
    print("\n=== 测试人物卡管理器 ===")
    try:
        config = ConfigLoader()
        character_manager = CharacterManager(config)
        
        # 创建测试角色
        characters = character_manager.create_main_characters(QUICK_START_CONFIG)
        
        print(f"✓ 角色创建成功")
        print(f"  角色数量: {len(characters)}")
        
        for char_id, char_data in characters.items():
            name = char_data['basic_info']['full_name']
            profession = char_data['basic_info']['profession']
            print(f"  - {name} ({profession})")
        
        return True
    except Exception as e:
        print(f"✗ 人物卡管理器测试失败: {str(e)}")
        return False

def test_worldview_manager():
    """测试世界观管理器"""
    print("\n=== 测试世界观管理器 ===")
    try:
        config = ConfigLoader()
        worldview_manager = WorldviewManager(config)
        
        # 创建测试世界观
        worldview = worldview_manager.create_worldview(QUICK_START_CONFIG)
        
        print(f"✓ 世界观创建成功")
        print(f"  地理区域数量: {len(worldview['geography'])}")
        print(f"  力量体系数量: {len(worldview['power_systems'])}")
        print(f"  社会结构数量: {len(worldview['social_structures'])}")
        print(f"  历史事件数量: {len(worldview['history'])}")
        
        return True
    except Exception as e:
        print(f"✗ 世界观管理器测试失败: {str(e)}")
        return False

def test_quality_controller():
    """测试质量控制器"""
    print("\n=== 测试质量控制器 ===")
    try:
        config = ConfigLoader()
        quality_controller = QualityController(config)
        
        # 创建测试章节
        test_chapter = {
            'number': 1,
            'title': '测试章节',
            'content': '这是一个测试章节的内容。林砚冰走在路上，思考着修炼的问题。',
            'mandatory_elements': ['伏笔：神秘玉佩', '设定植入：修炼体系'],
            'log': {
                'foreshadowing_management': {
                    'new_clues': [{'type': '道具', 'content': '玉佩发光'}]
                }
            }
        }
        
        # 创建测试项目配置
        test_project_config = {
            'characters': {},
            'worldview': {'magic_rules': {'禁忌法术': {}}}
        }
        
        # 执行质量检查
        quality_report = quality_controller.check_chapter(test_chapter, test_project_config)
        
        print(f"✓ 质量检查完成")
        print(f"  检查通过: {quality_report['passed']}")
        print(f"  总体评分: {quality_report['overall_score']:.2f}")
        print(f"  问题数量: {len(quality_report['issues'])}")
        
        return True
    except Exception as e:
        print(f"✗ 质量控制器测试失败: {str(e)}")
        return False

def test_template_loading():
    """测试模板加载"""
    print("\n=== 测试模板加载 ===")
    try:
        # 测试场景模板
        scene_template_file = Path("templates/scene_templates.json")
        if scene_template_file.exists():
            with open(scene_template_file, 'r', encoding='utf-8') as f:
                scene_templates = json.load(f)
            print(f"✓ 场景模板加载成功，模板数量: {len(scene_templates)}")
        
        # 测试章节模板
        chapter_template_file = Path("templates/chapter_templates.json")
        if chapter_template_file.exists():
            with open(chapter_template_file, 'r', encoding='utf-8') as f:
                chapter_templates = json.load(f)
            print(f"✓ 章节模板加载成功，模板数量: {len(chapter_templates)}")
        
        return True
    except Exception as e:
        print(f"✗ 模板加载测试失败: {str(e)}")
        return False

def test_data_directory():
    """测试数据目录创建"""
    print("\n=== 测试数据目录 ===")
    try:
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        projects_dir = data_dir / "projects"
        projects_dir.mkdir(exist_ok=True)
        
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        print("✓ 数据目录创建成功")
        print(f"  数据目录: {data_dir.absolute()}")
        print(f"  项目目录: {projects_dir.absolute()}")
        print(f"  日志目录: {logs_dir.absolute()}")
        
        return True
    except Exception as e:
        print(f"✗ 数据目录测试失败: {str(e)}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始系统测试...\n")
    
    tests = [
        ("配置加载器", test_config_loader),
        ("数据目录", test_data_directory),
        ("模板加载", test_template_loading),
        ("大纲管理器", test_outline_manager),
        ("人物卡管理器", test_character_manager),
        ("世界观管理器", test_worldview_manager),
        ("质量控制器", test_quality_controller),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}测试异常: {str(e)}")
    
    print(f"\n=== 测试结果 ===")
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统可以正常使用。")
        print("\n下一步：")
        print("1. 配置AI API密钥（编辑 config/ai_config.json）")
        print("2. 运行 python main.py 开始创作")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关模块。")

if __name__ == "__main__":
    run_all_tests()
