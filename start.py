#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI全自动写小说系统启动脚本
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误：需要Python 3.8或更高版本")
        print(f"当前版本：{sys.version}")
        return False
    print(f"✓ Python版本检查通过：{sys.version}")
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = ['requests', 'json5']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n缺少依赖包：{', '.join(missing_packages)}")
        print("请运行：pip install -r requirements.txt")
        return False
    
    return True

def check_config():
    """检查配置文件"""
    config_files = [
        'config/ai_config.json',
        'config/system_config.json'
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✓ {config_file} 存在")
        else:
            print(f"❌ {config_file} 不存在")
            return False
    
    return True

def create_directories():
    """创建必要的目录"""
    directories = ['data', 'data/projects', 'logs']
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ 目录 {directory} 已创建")
    
    return True

def show_welcome():
    """显示欢迎信息"""
    print("=" * 50)
    print("🎉 欢迎使用AI全自动写小说系统！")
    print("=" * 50)
    print()
    print("功能特性：")
    print("📖 三层结构化大纲设计（宏观-中观-微观）")
    print("👥 三维人物建模（基础层-动机层-行为层）")
    print("🔗 智能章节生成与记忆链管理")
    print("🎯 伏笔埋设与回收系统")
    print("🔍 自动质量控制与OOC检测")
    print("⚠️  风险应对与异常处理")
    print("🔧 弹性调节机制")
    print("📝 多场景提示词模板")
    print()

def show_quick_start():
    """显示快速开始指南"""
    print("🚀 快速开始：")
    print("1. 配置AI接口：编辑 config/ai_config.json，添加你的API密钥")
    print("2. 运行系统测试：python test_system.py")
    print("3. 启动主程序：python main.py")
    print("4. 选择创建新小说，输入基本信息")
    print("5. 系统将自动生成大纲、角色和世界观")
    print("6. 开始逐章生成或一次性生成完整小说")
    print()
    
    print("📚 示例配置：")
    print("- 查看 example_config.py 了解配置示例")
    print("- 建议先用较短的小说（10-20章）测试系统")
    print()
    
    print("⚙️  配置说明：")
    print("- config/ai_config.json: AI模型配置（必须配置API密钥）")
    print("- config/system_config.json: 系统参数配置")
    print("- templates/: 提示词模板（可自定义）")
    print()

def main():
    """主函数"""
    show_welcome()
    
    print("🔍 系统检查中...")
    print()
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查依赖包
    print("\n📦 检查依赖包...")
    if not check_dependencies():
        return
    
    # 检查配置文件
    print("\n⚙️  检查配置文件...")
    if not check_config():
        print("\n请确保配置文件存在且格式正确")
        return
    
    # 创建目录
    print("\n📁 创建必要目录...")
    create_directories()
    
    print("\n✅ 系统检查完成！")
    print()
    
    show_quick_start()
    
    # 询问是否运行测试
    while True:
        choice = input("是否运行系统测试？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            print("\n🧪 运行系统测试...")
            os.system("python test_system.py")
            break
        elif choice in ['n', 'no', '否']:
            break
        else:
            print("请输入 y 或 n")
    
    # 询问是否启动主程序
    print()
    while True:
        choice = input("是否启动主程序？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            print("\n🚀 启动AI小说创作系统...")
            os.system("python main.py")
            break
        elif choice in ['n', 'no', '否']:
            print("感谢使用AI小说创作系统！")
            break
        else:
            print("请输入 y 或 n")

if __name__ == "__main__":
    main()
