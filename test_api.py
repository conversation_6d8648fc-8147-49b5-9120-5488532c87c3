#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API连接
"""

import requests
import json

def test_claude_api():
    """测试Claude API连接"""
    print("测试Claude API连接...")
    
    base_url = "https://lazy-bear-13.deno.dev"
    api_key = "awdsadwasdas"
    
    # 测试不同的端点
    endpoints = [
        "/v1/messages",
        "/v1/chat/completions",
        "/api/v1/messages",
        "/messages",
        "/chat/completions"
    ]
    
    for endpoint in endpoints:
        print(f"\n尝试端点: {base_url}{endpoint}")
        
        # Claude格式
        claude_headers = {
            'x-api-key': api_key,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
        }
        
        claude_data = {
            "model": "claude-3-sonnet-20240229",
            "max_tokens": 100,
            "temperature": 0.8,
            "messages": [{"role": "user", "content": "你好，请回复一句话"}]
        }
        
        # OpenAI兼容格式
        openai_headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        openai_data = {
            "model": "claude-3-sonnet-20240229",
            "messages": [{"role": "user", "content": "你好，请回复一句话"}],
            "max_tokens": 100,
            "temperature": 0.8
        }
        
        # 尝试Claude格式
        try:
            print("  尝试Claude格式...")
            response = requests.post(
                f"{base_url}{endpoint}",
                headers=claude_headers,
                json=claude_data,
                timeout=30
            )
            
            print(f"  状态码: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"  响应: {json.dumps(result, indent=2, ensure_ascii=False)[:200]}...")
                return True
            else:
                print(f"  错误: {response.text[:200]}...")
                
        except Exception as e:
            print(f"  Claude格式失败: {str(e)}")
        
        # 尝试OpenAI格式
        try:
            print("  尝试OpenAI格式...")
            response = requests.post(
                f"{base_url}{endpoint}",
                headers=openai_headers,
                json=openai_data,
                timeout=30
            )
            
            print(f"  状态码: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"  响应: {json.dumps(result, indent=2, ensure_ascii=False)[:200]}...")
                return True
            else:
                print(f"  错误: {response.text[:200]}...")
                
        except Exception as e:
            print(f"  OpenAI格式失败: {str(e)}")
    
    return False

def test_simple_generation():
    """测试简单文本生成"""
    print("\n" + "="*50)
    print("测试简单文本生成")
    print("="*50)
    
    from ai_interface.ai_client import AIClient
    from utils.config_loader import ConfigLoader
    
    try:
        config = ConfigLoader()
        ai_config = config.get_ai_config()
        
        client = AIClient(ai_config)
        
        prompt = "请写一段关于程序员林浩的简短描述，大约100字。"
        
        print(f"发送提示词: {prompt}")
        print("正在生成...")
        
        result = client.generate_text(prompt)
        
        print(f"生成结果: {result}")
        return True
        
    except Exception as e:
        print(f"生成失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始API连接测试...")
    
    # 测试原始API
    api_success = test_claude_api()
    
    if api_success:
        print("\n✅ API连接成功！")
        # 测试集成生成
        gen_success = test_simple_generation()
        if gen_success:
            print("\n🎉 文本生成测试成功！")
        else:
            print("\n❌ 文本生成测试失败")
    else:
        print("\n❌ API连接失败，请检查配置")
