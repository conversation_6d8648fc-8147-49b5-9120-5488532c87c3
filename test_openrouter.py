#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OpenRouter API集成
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from ai_interface.ai_client import AIClient
from utils.config_loader import Config<PERSON>oader

def test_openrouter_api():
    """测试OpenRouter API"""
    print("🔍 测试OpenRouter API集成...")
    print("=" * 50)
    
    try:
        # 加载配置
        config = ConfigLoader()
        ai_config = config.get_ai_config()
        
        print(f"✅ 配置加载成功")
        print(f"   提供商: {ai_config['ai_models']['primary']['provider']}")
        print(f"   模型: {ai_config['ai_models']['primary']['model']}")
        print(f"   API地址: {ai_config['ai_models']['primary']['base_url']}")
        print()
        
        # 初始化AI客户端
        client = AIClient(ai_config)
        
        # 测试1：简单连接测试
        print("📝 测试1: 简单连接测试")
        test_prompt = "请用中文回复：API连接成功"
        result = client.generate_text(test_prompt)
        
        if result and "API连接成功" in result:
            print(f"✅ 连接测试通过")
            print(f"   响应: {result}")
        else:
            print(f"❌ 连接测试失败")
            print(f"   响应: {result}")
            return False
        
        print()
        
        # 测试2：中文创作测试
        print("📝 测试2: 中文小说创作测试")
        story_prompt = """请创作一个简短的都市小说开头，要求：
- 主角：程序员林浩，28岁
- 场景：深夜的办公室
- 情节：加班时意外发现了一个神秘系统
- 字数：300字左右
- 用中文创作

请开始创作："""
        
        story_result = client.generate_text(story_prompt)
        
        if story_result and len(story_result) > 200:
            print(f"✅ 中文创作测试通过")
            print(f"   生成字数: {len(story_result)}")
            print(f"   内容预览: {story_result[:150]}...")
        else:
            print(f"❌ 中文创作测试失败")
            print(f"   响应: {story_result}")
            return False
        
        print()
        
        # 测试3：系统提示词测试
        print("📝 测试3: 系统提示词测试")
        system_prompt = "你是一个专业的中文小说创作AI助手，擅长创作都市系统文小说。"
        user_prompt = "请为小说《多子多福系统》设计一个吸引人的开头情节。"
        
        system_result = client.generate_text(user_prompt, system_prompt)
        
        if system_result and len(system_result) > 100:
            print(f"✅ 系统提示词测试通过")
            print(f"   生成内容: {system_result}")
        else:
            print(f"❌ 系统提示词测试失败")
            print(f"   响应: {system_result}")
            return False
        
        print()
        print("🎉 所有OpenRouter API测试通过！")
        print("DeepSeek R1模型已成功集成到系统中。")
        return True
        
    except Exception as e:
        print(f"❌ OpenRouter API测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始OpenRouter API集成测试...")
    
    success = test_openrouter_api()
    
    if success:
        print("\n🎊 恭喜！OpenRouter API集成成功！")
        print("现在可以使用DeepSeek R1模型创作小说了。")
    else:
        print("\n😔 OpenRouter API集成失败，请检查配置。")
