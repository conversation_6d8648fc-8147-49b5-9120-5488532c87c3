#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的AI小说创作系统
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from main import NovelWritingSystem

def test_full_novel_system():
    """测试完整的小说创作系统"""
    print("🎯 测试完整的AI小说创作系统")
    print("=" * 60)
    
    try:
        # 创建系统实例
        print("🔧 初始化AI小说创作系统...")
        system = NovelWritingSystem()
        print("✅ 系统初始化成功")
        print()
        
        # 多子多福系统文配置
        novel_config = {
            "title": "多子多福系统",
            "genre": "都市系统文",
            "theme": "家庭、责任与成长",
            "main_plot": "普通程序员林浩意外获得多子多福系统，每生一个孩子就能获得丰厚奖励，从此走上人生巅峰的故事",
            "protagonist": {
                "name": "林浩",
                "age": 28,
                "occupation": "程序员",
                "personality": "善良、负责任、内向",
                "background": "普通家庭出身，大学毕业后在互联网公司工作，月薪8000，单身，对未来迷茫"
            },
            "heroine": {
                "name": "苏晴雨", 
                "age": 26,
                "occupation": "小学教师",
                "personality": "温柔、善良、喜欢孩子",
                "background": "林浩的大学同学，一直对林浩有好感"
            },
            "system_setting": {
                "name": "多子多福系统",
                "function": "每生育一个孩子获得递增奖励",
                "rewards": "金钱、技能、身体强化、特殊道具等",
                "progression": "第一个孩子100万，第二个200万，以此类推"
            },
            "total_chapters": 100,
            "target_words_per_chapter": 3000,
            "setting": "现代都市",
            "tone": "轻松幽默，正能量"
        }
        
        print("📚 创建小说项目...")
        print(f"   标题: {novel_config['title']}")
        print(f"   类型: {novel_config['genre']}")
        print(f"   主角: {novel_config['protagonist']['name']}")
        print(f"   女主: {novel_config['heroine']['name']}")
        print(f"   总章数: {novel_config['total_chapters']}")
        print()
        
        # 创建新小说项目
        project_dir = system.create_new_novel(novel_config)
        print(f"✅ 项目创建成功: {project_dir}")
        print()
        
        # 生成第一章
        print("📝 生成第一章...")
        print("-" * 40)
        
        chapter_1 = system.generate_chapter(project_dir, 1)
        
        if chapter_1 and chapter_1.get('content'):
            print("✅ 第一章生成成功！")
            print(f"   标题: {chapter_1.get('title', '第一章')}")
            print(f"   字数: {len(chapter_1['content'])}")
            print()
            
            # 显示内容预览
            content = chapter_1['content']
            preview_length = 300
            preview = content[:preview_length] + "..." if len(content) > preview_length else content
            
            print("📖 第一章内容预览:")
            print("=" * 50)
            print(f"第一章 {chapter_1.get('title', '')}")
            print()
            print(preview)
            print("=" * 50)
            print()
            
            # 测试生成第二章
            print("📝 生成第二章...")
            print("-" * 40)
            
            chapter_2 = system.generate_chapter(project_dir, 2)
            
            if chapter_2 and chapter_2.get('content'):
                print("✅ 第二章生成成功！")
                print(f"   标题: {chapter_2.get('title', '第二章')}")
                print(f"   字数: {len(chapter_2['content'])}")
                print()
                
                # 显示第二章预览
                content_2 = chapter_2['content']
                preview_2 = content_2[:preview_length] + "..." if len(content_2) > preview_length else content_2
                
                print("📖 第二章内容预览:")
                print("=" * 50)
                print(f"第二章 {chapter_2.get('title', '')}")
                print()
                print(preview_2)
                print("=" * 50)
                print()
                
                # 系统功能测试总结
                print("🎉 系统功能测试完成！")
                print("=" * 60)
                print("✅ 测试结果:")
                print(f"   ✓ 系统初始化: 成功")
                print(f"   ✓ 项目创建: 成功")
                print(f"   ✓ 大纲生成: 成功")
                print(f"   ✓ 角色设定: 成功")
                print(f"   ✓ 世界观设定: 成功")
                print(f"   ✓ 第一章生成: 成功 ({len(chapter_1['content'])}字)")
                print(f"   ✓ 第二章生成: 成功 ({len(chapter_2['content'])}字)")
                print()
                print("📁 项目文件位置:")
                print(f"   {project_dir}")
                print()
                print("🎊 恭喜！AI小说创作系统完全可用！")
                
                return True
                
            else:
                print("❌ 第二章生成失败")
                print("第一章生成成功，但第二章有问题")
                return False
                
        else:
            print("❌ 第一章生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 系统测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试完整的AI小说创作系统...")
    print()
    
    success = test_full_novel_system()
    
    if success:
        print()
        print("🎉 测试完成！系统运行正常！")
        print("您现在可以使用以下方式启动系统:")
        print("   python start.py")
        print("   或")
        print("   python main.py")
    else:
        print()
        print("😔 系统测试失败，请检查配置和网络连接。")
