#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI客户端接口
"""

import json
import time
import requests
from typing import Dict, List, Any, Optional
from utils.logger import LoggerMixin

class AIClient(LoggerMixin):
    """AI客户端类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.models = config.get('ai_models', {})
        self.generation_settings = config.get('generation_settings', {})
        self.current_model = 'primary'
    
    def generate_text(self, prompt: str, system_prompt: str = None, **kwargs) -> str:
        """生成文本"""
        max_retries = self.generation_settings.get('retry_attempts', 3)
        
        for attempt in range(max_retries):
            try:
                model_config = self.models.get(self.current_model, {})
                provider = model_config.get('provider', 'openai')
                
                if provider == 'openai':
                    return self._call_openai(prompt, system_prompt, model_config, **kwargs)
                elif provider == 'claude':
                    return self._call_claude(prompt, system_prompt, model_config, **kwargs)
                elif provider == 'ollama':
                    return self._call_ollama(prompt, system_prompt, model_config, **kwargs)
                else:
                    raise ValueError(f"不支持的AI提供商: {provider}")
                    
            except Exception as e:
                self.logger.warning(f"AI调用失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                
                if attempt < max_retries - 1:
                    # 尝试切换到备用模型
                    if self.generation_settings.get('fallback_enabled', True):
                        self._switch_to_fallback()
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    raise
        
        raise Exception("所有AI调用尝试都失败了")
    
    def _call_openai(self, prompt: str, system_prompt: str, config: Dict, **kwargs) -> str:
        """调用OpenAI API"""
        headers = {
            'Authorization': f"Bearer {config.get('api_key')}",
            'Content-Type': 'application/json'
        }
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        data = {
            "model": config.get('model', 'gpt-4'),
            "messages": messages,
            "max_tokens": config.get('max_tokens', 4000),
            "temperature": config.get('temperature', 0.8),
            **kwargs
        }
        
        response = requests.post(
            f"{config.get('base_url')}/chat/completions",
            headers=headers,
            json=data,
            timeout=self.generation_settings.get('timeout', 60)
        )
        
        response.raise_for_status()
        result = response.json()
        
        return result['choices'][0]['message']['content']
    
    def _call_claude(self, prompt: str, system_prompt: str, config: Dict, **kwargs) -> str:
        """调用Claude API"""
        headers = {
            'x-api-key': config.get('api_key'),
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
        }
        
        messages = [{"role": "user", "content": prompt}]
        
        data = {
            "model": config.get('model', 'claude-3-sonnet-20240229'),
            "max_tokens": config.get('max_tokens', 4000),
            "temperature": config.get('temperature', 0.8),
            "messages": messages,
            **kwargs
        }
        
        if system_prompt:
            data["system"] = system_prompt
        
        response = requests.post(
            f"{config.get('base_url')}/v1/messages",
            headers=headers,
            json=data,
            timeout=self.generation_settings.get('timeout', 60)
        )
        
        response.raise_for_status()
        result = response.json()
        
        return result['content'][0]['text']
    
    def _call_ollama(self, prompt: str, system_prompt: str, config: Dict, **kwargs) -> str:
        """调用Ollama本地API"""
        full_prompt = prompt
        if system_prompt:
            full_prompt = f"{system_prompt}\n\n{prompt}"
        
        data = {
            "model": config.get('model', 'qwen2:7b'),
            "prompt": full_prompt,
            "stream": False,
            "options": {
                "temperature": config.get('temperature', 0.8),
                "num_predict": config.get('max_tokens', 4000)
            }
        }
        
        response = requests.post(
            f"{config.get('base_url')}/api/generate",
            json=data,
            timeout=self.generation_settings.get('timeout', 60)
        )
        
        response.raise_for_status()
        result = response.json()
        
        return result['response']
    
    def _switch_to_fallback(self):
        """切换到备用模型"""
        fallback_order = ['secondary', 'local', 'primary']
        current_index = fallback_order.index(self.current_model) if self.current_model in fallback_order else 0
        
        for i in range(1, len(fallback_order)):
            next_index = (current_index + i) % len(fallback_order)
            next_model = fallback_order[next_index]
            
            if next_model in self.models:
                self.current_model = next_model
                self.logger.info(f"切换到备用模型: {next_model}")
                break
    
    def batch_generate(self, prompts: List[str], system_prompt: str = None, **kwargs) -> List[str]:
        """批量生成文本"""
        results = []
        for i, prompt in enumerate(prompts):
            self.logger.info(f"批量生成进度: {i+1}/{len(prompts)}")
            result = self.generate_text(prompt, system_prompt, **kwargs)
            results.append(result)
        return results
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取当前模型信息"""
        return {
            'current_model': self.current_model,
            'model_config': self.models.get(self.current_model, {}),
            'available_models': list(self.models.keys())
        }
