#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节生成器 - 构建可追溯的记忆链
"""

import json
import uuid
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from utils.logger import LoggerMixin

@dataclass
class ChapterLog:
    """章节日志"""
    chapter_number: int
    title: str
    plot_anchor: Dict[str, Any]  # 情节锚点
    foreshadowing_management: Dict[str, Any]  # 伏笔管理
    character_state_changes: Dict[str, Any]  # 人物状态变更
    unresolved_mysteries: List[str]  # 未解悬念
    word_count: int
    created_at: str

@dataclass
class Chapter:
    """章节完整信息"""
    number: int
    title: str
    content: str
    scene_setting: Dict[str, str]  # 场景设定
    core_event: str
    character_goals: Dict[str, str]
    emotional_tone: List[str]
    mandatory_elements: List[str]
    log: ChapterLog
    created_at: str

class ChapterGenerator(LoggerMixin):
    """章节生成器"""
    
    def __init__(self, ai_client, outline_manager, character_manager, worldview_manager):
        self.ai_client = ai_client
        self.outline_manager = outline_manager
        self.character_manager = character_manager
        self.worldview_manager = worldview_manager
    
    def generate_chapter(self, project_config: Dict, chapter_num: int) -> Dict[str, Any]:
        """生成指定章节"""
        self.logger.info(f"开始生成第{chapter_num}章")
        
        # 获取大纲信息
        outline_info = self.outline_manager.get_chapter_outline(
            project_config['outline'], chapter_num
        )
        
        # 获取角色状态
        character_states = self._get_character_states(
            project_config['characters'], chapter_num
        )
        
        # 获取世界观信息
        worldview_info = project_config['worldview']
        
        # 构建章节生成提示词
        prompt = self._build_chapter_prompt(
            outline_info, character_states, worldview_info, chapter_num
        )
        
        # 调用AI生成章节内容
        content = self.ai_client.generate_text(
            prompt, 
            system_prompt=self._get_system_prompt()
        )
        
        # 创建章节对象
        chapter = self._create_chapter_object(
            chapter_num, content, outline_info, character_states
        )
        
        # 创建章节日志
        chapter_log = self._create_chapter_log(
            chapter, outline_info, character_states
        )
        
        chapter['log'] = asdict(chapter_log)
        
        self.logger.info(f"第{chapter_num}章生成完成，字数：{len(content)}")
        return chapter
    
    def _get_character_states(self, characters: Dict, chapter_num: int) -> Dict[str, Any]:
        """获取所有角色在当前章节的状态"""
        character_states = {}
        for char_id, char_data in characters.items():
            state = self.character_manager.get_character_state(
                characters, char_id, chapter_num
            )
            character_states[char_id] = {
                'name': char_data['basic_info']['full_name'],
                'state': state,
                'basic_info': char_data['basic_info'],
                'motivation': char_data['motivation'],
                'behavior': char_data['behavior']
            }
        return character_states
    
    def _build_chapter_prompt(self, outline_info: Dict, character_states: Dict, 
                            worldview_info: Dict, chapter_num: int) -> str:
        """构建章节生成提示词"""
        
        if not outline_info:
            return self._build_fallback_prompt(chapter_num)
        
        plot_unit = outline_info.get('plot_unit', {})
        act = outline_info.get('act', {})
        
        # 场景定位
        scene_setting = self._generate_scene_setting(plot_unit, worldview_info)
        
        # 核心事件
        core_event = self._generate_core_event(plot_unit, chapter_num)
        
        # 人物目标
        character_goals = self._generate_character_goals(character_states, plot_unit)
        
        # 情感基调
        emotional_tone = plot_unit.get('emotion_curve', ['平静', '紧张', '决心'])
        
        # 强制元素
        mandatory_elements = self._get_mandatory_elements(plot_unit, act, chapter_num)
        
        prompt = f"""
请根据以下设定创作一个小说章节：

【场景定位】
时间：{scene_setting.get('time', '未指定时间')}
地点：{scene_setting.get('location', '未指定地点')}
天气：{scene_setting.get('weather', '晴朗')}

【核心事件】
{core_event}

【人物目标】
{self._format_character_goals(character_goals)}

【情感基调】
{' / '.join(emotional_tone[:3])}

【强制元素】
{self._format_mandatory_elements(mandatory_elements)}

【角色信息】
{self._format_character_info(character_states)}

【世界观规则】
{self._format_worldview_rules(worldview_info, scene_setting.get('location', ''))}

请创作一个3000字左右的章节，要求：
1. 严格按照角色性格和行为模式描写
2. 包含所有强制元素
3. 情感曲线符合设定
4. 对话生动自然，符合角色特点
5. 环境描写与世界观设定一致
6. 适当埋设伏笔，为后续剧情做铺垫
"""
        
        return prompt
    
    def _build_fallback_prompt(self, chapter_num: int) -> str:
        """构建备用提示词（当大纲信息不足时）"""
        return f"""
请创作小说第{chapter_num}章，要求：
1. 推进主线剧情
2. 保持角色一致性
3. 字数3000字左右
4. 情节紧凑有趣
"""
    
    def _generate_scene_setting(self, plot_unit: Dict, worldview_info: Dict) -> Dict[str, str]:
        """生成场景设定"""
        # 简化实现，实际应该更智能
        return {
            'time': '秋分时节的戌初时分',
            'location': '雾隐森林边缘的破旧驿站',
            'weather': '细雨中夹杂着硫磺味'
        }
    
    def _generate_core_event(self, plot_unit: Dict, chapter_num: int) -> str:
        """生成核心事件"""
        core_goal = plot_unit.get('core_goal', '')
        if core_goal:
            return f"围绕「{core_goal}」展开的关键情节"
        return f"推进第{chapter_num}章的主要剧情发展"
    
    def _generate_character_goals(self, character_states: Dict, plot_unit: Dict) -> Dict[str, str]:
        """生成角色目标"""
        goals = {}
        for char_id, char_info in character_states.items():
            char_name = char_info['name']
            if char_name == '林砚冰':  # 主角
                goals[char_name] = plot_unit.get('core_goal', '完成当前阶段的修炼任务')
            else:
                goals[char_name] = f"根据与主角的关系推进各自的目标"
        return goals
    
    def _get_mandatory_elements(self, plot_unit: Dict, act: Dict, chapter_num: int) -> List[str]:
        """获取强制元素"""
        elements = []
        
        # 从情节单元获取伏笔
        foreshadowing = plot_unit.get('foreshadowing', [])
        for fh in foreshadowing:
            elements.append(f"伏笔：{fh.get('content', '')}")
        
        # 从幕结构获取核心设定
        core_settings = act.get('core_settings', [])
        for setting in core_settings:
            elements.append(f"设定植入：{setting}")
        
        return elements
    
    def _format_character_goals(self, goals: Dict[str, str]) -> str:
        """格式化角色目标"""
        formatted = []
        for char_name, goal in goals.items():
            formatted.append(f"{char_name}：{goal}")
        return '\n'.join(formatted)
    
    def _format_mandatory_elements(self, elements: List[str]) -> str:
        """格式化强制元素"""
        if not elements:
            return "无特殊要求"
        return '\n'.join([f"- {element}" for element in elements])
    
    def _format_character_info(self, character_states: Dict) -> str:
        """格式化角色信息"""
        formatted = []
        for char_id, char_info in character_states.items():
            name = char_info['name']
            basic = char_info['basic_info']
            motivation = char_info['motivation']
            
            char_desc = f"""
{name}：
- 外貌：{basic.get('appearance', '')}
- 职业：{basic.get('profession', '')}
- 核心欲望：{motivation.get('core_desire', '')}
- 当前状态：{char_info['state'].get('emotional_state', '')}
"""
            formatted.append(char_desc)
        
        return '\n'.join(formatted)
    
    def _format_worldview_rules(self, worldview_info: Dict, location: str) -> str:
        """格式化世界观规则"""
        # 获取地点相关规则
        location_info = self.worldview_manager.get_location_info(worldview_info, location)
        
        rules = []
        if location_info:
            special_rules = location_info.get('special_rules', [])
            for rule in special_rules:
                rules.append(f"- {rule.get('rule', '')}：{rule.get('effect', '')}")
        
        # 获取魔法规则
        magic_rules = self.worldview_manager.check_magic_rules(worldview_info, '通用', location)
        if magic_rules.get('location_effects') != "无特殊影响":
            rules.append(f"- 环境影响：{magic_rules.get('location_effects', '')}")
        
        return '\n'.join(rules) if rules else "无特殊规则限制"
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是一个专业的小说创作AI助手，擅长根据大纲和人物设定创作高质量的小说章节。

创作要求：
1. 严格遵循角色设定，不能出现OOC（角色崩坏）
2. 情节发展要符合逻辑，与前文保持一致
3. 对话要生动自然，符合角色性格特点
4. 环境描写要详细，营造沉浸感
5. 适当使用隐喻和象征手法
6. 控制节奏，张弛有度
7. 字数控制在2500-3500字之间

禁止事项：
1. 不要使用"突然"、"忽然"等突兀的转折词
2. 爱情场景禁用"爱"、"喜欢"等直白词汇
3. 不要让角色做出违背设定的行为
4. 不要随意改变世界观规则
"""
    
    def _create_chapter_object(self, chapter_num: int, content: str, 
                             outline_info: Dict, character_states: Dict) -> Dict[str, Any]:
        """创建章节对象"""
        plot_unit = outline_info.get('plot_unit', {})
        
        return {
            'number': chapter_num,
            'title': plot_unit.get('title', f'第{chapter_num}章'),
            'content': content,
            'scene_setting': self._generate_scene_setting(plot_unit, {}),
            'core_event': self._generate_core_event(plot_unit, chapter_num),
            'character_goals': self._generate_character_goals(character_states, plot_unit),
            'emotional_tone': plot_unit.get('emotion_curve', ['平静']),
            'mandatory_elements': self._get_mandatory_elements(plot_unit, outline_info.get('act', {}), chapter_num),
            'created_at': self._get_timestamp()
        }
    
    def _create_chapter_log(self, chapter: Dict, outline_info: Dict, 
                          character_states: Dict) -> ChapterLog:
        """创建章节日志"""
        plot_unit = outline_info.get('plot_unit', {})
        
        # 情节锚点
        plot_anchor = {
            'outline_node': f"第{outline_info.get('act', {}).get('act_number', 0)}幕",
            'plot_unit_id': plot_unit.get('id', ''),
            'core_event_summary': chapter['core_event'][:100]
        }
        
        # 伏笔管理
        foreshadowing_management = {
            'new_clues': self._extract_new_foreshadowing(chapter['content']),
            'resolved_clues': []  # 需要实现伏笔回收检测
        }
        
        # 人物状态变更
        character_state_changes = self._detect_character_changes(
            chapter['content'], character_states
        )
        
        # 未解悬念
        unresolved_mysteries = self._extract_mysteries(chapter['content'])
        
        return ChapterLog(
            chapter_number=chapter['number'],
            title=chapter['title'],
            plot_anchor=plot_anchor,
            foreshadowing_management=foreshadowing_management,
            character_state_changes=character_state_changes,
            unresolved_mysteries=unresolved_mysteries,
            word_count=len(chapter['content']),
            created_at=self._get_timestamp()
        )
    
    def _extract_new_foreshadowing(self, content: str) -> List[Dict[str, str]]:
        """提取新埋设的伏笔（简化实现）"""
        # 实际应该使用NLP技术分析文本
        return [
            {
                'type': '道具',
                'content': '神秘物品的出现',
                'significance': '为后续剧情埋下伏笔'
            }
        ]
    
    def _detect_character_changes(self, content: str, character_states: Dict) -> Dict[str, Any]:
        """检测角色状态变化（简化实现）"""
        # 实际应该分析文本中的角色行为和状态描述
        return {
            'protagonist': {
                'attribute_changes': '实力提升',
                'relationship_changes': '与某角色关系加深',
                'emotional_changes': '更加坚定'
            }
        }
    
    def _extract_mysteries(self, content: str) -> List[str]:
        """提取未解悬念（简化实现）"""
        # 实际应该分析文本中的疑问和悬念
        return [
            '神秘人物的真实身份',
            '古老预言的含义',
            '失踪事件的真相'
        ]
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
