#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟生成多子多福系统文小说（使用预设内容）
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from duozi_novel_config import DUOZI_NOVEL_CONFIG

# 预设的章节内容
MOCK_CHAPTERS = {
    1: {
        "title": "平凡的程序员",
        "content": """
深夜十一点，CBD写字楼里依然灯火通明。

林浩揉了揉酸涩的眼睛，看着屏幕上密密麻麻的代码，长长地叹了一口气。作为一名28岁的程序员，他已经在这家互联网公司工作了三年，每天重复着写代码、改bug、加班的生活。

"又是一个通宵达旦的夜晚。"林浩自嘲地笑了笑，端起已经凉透的咖啡喝了一口。

办公室里只剩下他一个人，同事们早就下班回家陪家人了。而他，一个单身狗，除了工作还是工作。月薪八千，在这个城市里只能勉强维持生活，租住在一间十几平米的小单间里。

"林浩，你还在啊？"项目经理王总路过他的工位，"这个功能明天上线，你再检查一遍。"

"好的，王总。"林浩点点头，继续埋头工作。

他想起了大学时的梦想，想起了那个温柔的女孩苏晴雨。她现在在做什么呢？是不是已经结婚生子，过着幸福的生活？

林浩摇摇头，将这些杂念抛开，继续专注于代码。生活就是这样，平淡无奇，没有惊喜，也没有希望。

直到那一天，一切都改变了...

窗外的城市霓虹闪烁，仿佛在诉说着无数人的故事。而林浩的故事，才刚刚开始。
""",
        "word_count": 387
    },
    2: {
        "title": "神秘系统降临",
        "content": """
凌晨两点，林浩终于完成了所有工作。他收拾好东西，拖着疲惫的身体走出办公楼。

夜风有些凉，街道上空无一人，只有路灯孤独地亮着。林浩走向地铁站，准备回到那个冰冷的小房间。

就在这时，一阵眩晕感突然袭来。

"怎么回事？"林浩扶着路边的栏杆，感觉头晕目眩。

【叮！检测到宿主符合绑定条件】

【多子多福系统正在激活...】

【激活成功！欢迎使用多子多福系统！】

林浩愣住了，这个声音直接在他脑海中响起，清晰得就像有人在他耳边说话。

"什么系统？我是不是太累了，出现幻觉了？"林浩用力摇摇头。

【宿主您好，我是多子多福系统。本系统旨在帮助宿主建立幸福家庭，每生育一个孩子，系统将给予丰厚奖励】

【系统功能包括：金钱奖励、技能提升、身体强化、特殊道具等】

【首次绑定奖励：魅力值+10，沟通技巧+5，获得'完美告白'技能】

林浩感觉到身体一阵暖流涌过，整个人的精神状态瞬间好了很多。他看了看手机前置摄像头里的自己，似乎确实比之前更有精神了。

"这...这是真的？"林浩不敢置信。

【系统提示：建议宿主尽快找到合适的伴侣，开启系统功能。检测到宿主记忆中存在合适目标：苏晴雨】

听到这个名字，林浩的心跳加速了。苏晴雨，他大学时的同班同学，那个温柔善良的女孩，他暗恋了四年却从未表白的人。

"系统，你是说...我可以去找晴雨？"

【系统建议：主动联系目标对象，运用新获得的技能建立感情基础】

林浩深吸一口气，看着夜空中的星星，第一次感觉到了希望的光芒。

也许，他的人生真的要改变了。
""",
        "word_count": 512
    },
    3: {
        "title": "重逢的机会",
        "content": """
第二天是周六，林浩破天荒地没有加班。他鼓起勇气，翻出了苏晴雨的微信号。

两人已经三年没有联系了，自从大学毕业后就各奔东西。林浩看着聊天记录，最后一条消息还是毕业那天的"以后要保持联系哦"。

【系统提示：使用'完美告白'技能，成功率将大幅提升】

林浩深吸一口气，开始打字：

"晴雨，好久不见，最近怎么样？我在想，要不要找个时间聚聚？"

发送后，林浩紧张地等待着回复。

十分钟后，手机震动了。

"林浩！好久不见！我还以为你把我忘了呢。我现在在市区小学当老师，你呢？"

看到回复，林浩心中一阵狂喜。系统的技能真的有用！

"我在做程序员，工作挺忙的。今天难得有空，想起了大学时光。你现在有空吗？我们出来见见面？"

"好啊！我正好也没什么事。老地方见？"

老地方，是他们大学时经常去的那家咖啡厅。林浩没想到她还记得。

一个小时后，林浩站在咖啡厅门口，看到了那个熟悉的身影。

苏晴雨还是那么美，温柔的笑容，清澈的眼神，穿着简单的白色连衣裙，就像大学时一样。

"林浩！"苏晴雨挥手打招呼，脸上带着惊喜的表情。

"晴雨，你还是那么漂亮。"林浩走过去，感觉到系统技能的加持，说话比以前自然多了。

"你也是，比大学时更成熟了。"苏晴雨笑着说，"快坐下，我们好好聊聊。"

两人坐下后，开始聊起了这些年的经历。苏晴雨说起了她的教师生活，那些可爱的学生，那些有趣的故事。林浩也分享了自己的工作，虽然辛苦，但也有成就感。

"你知道吗，我一直都记得你。"苏晴雨突然说道，脸颊微红，"大学时你总是默默帮助我，我都记在心里。"

林浩心中一震，原来她也...

【系统提示：目标对象好感度上升，建议继续深入交流】

"我也是，晴雨。这些年我一直想联系你，但是..."

"但是什么？"

"但是我觉得自己配不上你。"林浩诚实地说道。

苏晴雨看着他，眼中闪烁着温柔的光芒："林浩，你知道吗？真诚是最珍贵的品质。"

那一刻，林浩知道，他的机会来了。
""",
        "word_count": 678
    },
    4: {
        "title": "告白与承诺",
        "content": """
咖啡厅里，温暖的灯光洒在两人身上。林浩看着苏晴雨，心中涌起了前所未有的勇气。

"晴雨，我有话想对你说。"林浩深吸一口气。

"什么话？"苏晴雨好奇地看着他。

【系统提示：'完美告白'技能激活，请宿主真诚表达内心感受】

"我喜欢你，从大学第一次见到你就喜欢你。"林浩直视着她的眼睛，"这些年我一直在想你，想着如果当初勇敢一点就好了。"

苏晴雨愣住了，脸颊瞬间红了起来。

"我知道我现在只是一个普通的程序员，没有房子，没有车，甚至连像样的存款都没有。但是我想告诉你，我会努力的，我会给你幸福的。"

林浩继续说道："如果你愿意给我一个机会，我想和你在一起，想和你组建一个家庭，想和你一起面对生活的所有挑战。"

苏晴雨的眼中闪烁着泪光，她轻声说道："林浩，你知道吗？我也一直在等你。"

"什么？"林浩不敢置信。

"大学时我就觉得你很特别，你善良、真诚、有责任心。虽然你不善言辞，但我能感受到你的真心。"苏晴雨伸出手，轻轻握住了林浩的手，"这些年我也想过你，想着如果你能主动一点就好了。"

林浩感觉心脏快要跳出来了："那你的意思是..."

"我愿意和你在一起。"苏晴雨羞涩地说道，"我们一起努力，一起创造属于我们的未来。"

【系统提示：恭喜宿主成功建立恋爱关系！】

【奖励发放：金钱+50000元，魅力值+5，获得'理财技能'】

【新任务发布：与目标对象结婚，奖励：金钱+500000元，房产一套】

林浩感觉到手机震动了一下，银行短信显示账户多了五万元。他简直不敢相信这一切都是真的。

"林浩，你怎么了？"苏晴雨关心地问道。

"没什么，只是太高兴了。"林浩握紧了她的手，"晴雨，我保证，我会让你幸福的。"

"我相信你。"苏晴雨甜甜地笑了。

那天晚上，两人手牵手走在街上，就像大学时梦想的那样。林浩知道，他的新生活开始了。

而这，只是一个开始。

【系统提示：建议宿主尽快推进关系，早日完成结婚任务】

林浩看着身边的苏晴雨，心中充满了对未来的憧憬。有了系统的帮助，他一定能给她最好的生活。
""",
        "word_count": 712
    },
    5: {
        "title": "求婚与系统奖励",
        "content": """
三个月后，林浩和苏晴雨的感情越来越深厚。在系统的帮助下，林浩不仅在工作上表现出色，还学会了很多浪漫的技巧。

这天是苏晴雨的生日，林浩决定给她一个惊喜。

【系统提示：检测到宿主准备求婚，建议选择有纪念意义的地点】

林浩选择了他们第一次约会的那家咖啡厅。他提前和老板商量好，在咖啡厅里布置了鲜花和蜡烛。

"林浩，今天怎么这么神秘？"苏晴雨被蒙着眼睛带进咖啡厅。

"你马上就知道了。"林浩温柔地说道。

当苏晴雨睁开眼睛时，看到了满屋的玫瑰花和温暖的烛光。咖啡厅里播放着她最喜欢的音乐。

"这是..."苏晴雨捂住嘴巴，眼中满含泪水。

林浩单膝跪下，拿出了一枚钻戒："晴雨，这三个月是我人生中最快乐的时光。你让我明白了什么是爱，什么是幸福。"

"我想和你一起度过余生，想和你组建一个温暖的家庭，想和你一起看着我们的孩子长大。"林浩深情地说道，"苏晴雨，你愿意嫁给我吗？"

苏晴雨泪流满面，用力点头："我愿意！我愿意！"

林浩为她戴上戒指，两人紧紧拥抱在一起。咖啡厅里响起了掌声，原来老板和几个客人都在为他们祝福。

【系统提示：恭喜宿主求婚成功！】

【结婚任务激活：请在三个月内完成婚礼】

【预付奖励发放：金钱+200000元，获得'完美婚礼策划'技能】

【系统商城开启：可购买各种育儿用品和家庭用品】

林浩感觉到手机又震动了，银行余额瞬间增加了二十万。加上之前的奖励，他现在已经有了近三十万的存款。

"林浩，我们什么时候结婚？"苏晴雨幸福地问道。

"越快越好。"林浩笑着说，"我已经等不及要和你组建我们的小家庭了。"

"那我们的孩子..."苏晴雨脸红了。

"我希望我们能有很多孩子，一个大家庭，热热闹闹的。"林浩说道，心中想着系统的奖励机制。

【系统提示：每生育一个孩子，奖励将递增。第一个孩子奖励100万，第二个孩子奖励200万，以此类推】

【特殊提示：生育双胞胎或多胞胎将获得额外奖励】

苏晴雨点点头："我也喜欢孩子，我们一定会是很好的父母。"

那天晚上，两人开始规划他们的未来。结婚、买房、生孩子、教育孩子...每一个计划都让他们充满期待。

林浩知道，有了系统的帮助，他们的未来将会无比美好。而这个多子多福的系统，将会让他们拥有一个真正幸福的大家庭。

【系统提示：宿主表现优秀，系统将持续提供支持。请继续努力，建设美满家庭！】

林浩看着身边熟睡的苏晴雨，心中充满了感激。感谢命运，感谢系统，让他遇到了生命中最重要的人。

明天，他们就要开始筹备婚礼了。而在不久的将来，他们将迎来第一个孩子，开启真正的多子多福人生。
""",
        "word_count": 892
    }
}

def create_mock_novel():
    """创建模拟小说项目"""
    print("=" * 50)
    print("🎉 创建多子多福系统文（模拟版本）")
    print("=" * 50)
    
    # 创建项目目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    project_name = f"多子多福系统_模拟_{timestamp}"
    project_dir = Path("data/projects") / project_name
    project_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建子目录
    (project_dir / "chapters").mkdir(exist_ok=True)
    (project_dir / "logs").mkdir(exist_ok=True)
    (project_dir / "backups").mkdir(exist_ok=True)
    
    print(f"✅ 项目目录创建完成：{project_dir}")
    
    # 保存项目配置
    project_config = {
        'novel_config': DUOZI_NOVEL_CONFIG,
        'outline': {
            'title': DUOZI_NOVEL_CONFIG['title'],
            'total_chapters': DUOZI_NOVEL_CONFIG['total_chapters'],
            'acts': [
                {
                    'act_number': 1,
                    'name': '开端',
                    'chapter_start': 1,
                    'chapter_end': 30,
                    'description': '系统觉醒，建立感情基础'
                },
                {
                    'act_number': 2,
                    'name': '发展',
                    'chapter_start': 31,
                    'chapter_end': 80,
                    'description': '结婚生子，事业发展'
                },
                {
                    'act_number': 3,
                    'name': '高潮',
                    'chapter_start': 81,
                    'chapter_end': 100,
                    'description': '家庭圆满，人生巅峰'
                }
            ]
        },
        'characters': {
            'protagonist': DUOZI_NOVEL_CONFIG['protagonist'],
            'heroine': DUOZI_NOVEL_CONFIG['heroine'],
            'supporting': DUOZI_NOVEL_CONFIG['supporting_characters']
        },
        'worldview': {
            'system_settings': DUOZI_NOVEL_CONFIG['system_settings'],
            'modern_city': '现代都市背景，真实社会环境'
        }
    }
    
    config_file = project_dir / "project_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(project_config, f, ensure_ascii=False, indent=2)
    
    print("✅ 项目配置保存完成")
    
    # 生成前五章
    print("\n开始生成前五章...")
    print("-" * 30)
    
    chapters_info = []
    total_words = 0
    
    for i in range(1, 6):
        chapter_data = MOCK_CHAPTERS[i]
        
        print(f"📝 生成第{i}章：{chapter_data['title']}")
        
        # 创建完整的章节对象
        chapter = {
            'number': i,
            'title': chapter_data['title'],
            'content': chapter_data['content'].strip(),
            'word_count': chapter_data['word_count'],
            'scene_setting': {
                'time': '现代都市',
                'location': '城市各处',
                'weather': '适宜'
            },
            'core_event': DUOZI_NOVEL_CONFIG['first_five_chapters'][i-1]['main_events'][0],
            'character_goals': {
                '林浩': DUOZI_NOVEL_CONFIG['first_five_chapters'][i-1]['main_events'][0]
            },
            'emotional_tone': ['温馨', '浪漫', '励志'],
            'mandatory_elements': DUOZI_NOVEL_CONFIG['first_five_chapters'][i-1]['key_elements'],
            'log': {
                'chapter_number': i,
                'title': chapter_data['title'],
                'plot_anchor': {
                    'outline_node': '第1幕',
                    'core_event_summary': DUOZI_NOVEL_CONFIG['first_five_chapters'][i-1]['main_events'][0]
                },
                'foreshadowing_management': {
                    'new_clues': [
                        {
                            'type': '系统',
                            'content': '多子多福系统的神秘力量',
                            'significance': '推动后续剧情发展'
                        }
                    ],
                    'resolved_clues': []
                },
                'character_state_changes': {
                    'protagonist': {
                        'attribute_changes': '魅力值提升，获得新技能',
                        'relationship_changes': '与苏晴雨关系进展',
                        'emotional_changes': '从迷茫到充满希望'
                    }
                },
                'unresolved_mysteries': [
                    '系统的真正来源',
                    '为什么选择了林浩',
                    '系统的最终目的'
                ],
                'word_count': chapter_data['word_count'],
                'created_at': datetime.now().isoformat()
            },
            'created_at': datetime.now().isoformat()
        }
        
        # 保存章节文件
        chapter_file = project_dir / "chapters" / f"chapter_{i:03d}.json"
        with open(chapter_file, 'w', encoding='utf-8') as f:
            json.dump(chapter, f, ensure_ascii=False, indent=2)
        
        # 保存纯文本版本
        text_file = project_dir / "chapters" / f"chapter_{i:03d}.txt"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(f"第{i}章 {chapter_data['title']}\n\n")
            f.write(chapter_data['content'].strip())
        
        chapters_info.append({
            'number': i,
            'title': chapter_data['title'],
            'word_count': chapter_data['word_count'],
            'status': 'success'
        })
        
        total_words += chapter_data['word_count']
        
        print(f"✅ 第{i}章生成完成")
        print(f"   标题：{chapter_data['title']}")
        print(f"   字数：{chapter_data['word_count']}")
        print(f"   预览：{chapter_data['content'][:100].strip()}...")
        print()
    
    # 生成总结报告
    print("=" * 50)
    print("📊 生成总结报告")
    print("=" * 50)
    
    print(f"✅ 成功生成章节：5/5")
    print(f"📝 总字数：{total_words}")
    print(f"📊 平均字数：{total_words // 5}")
    print()
    
    # 保存生成报告
    report = {
        'novel_config': DUOZI_NOVEL_CONFIG,
        'chapters_info': chapters_info,
        'summary': {
            'total_chapters': 5,
            'successful_chapters': 5,
            'failed_chapters': 0,
            'total_words': total_words,
            'average_words': total_words // 5
        },
        'generated_at': datetime.now().isoformat()
    }
    
    report_file = project_dir / "generation_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("📁 项目文件位置：")
    print(f"   项目目录：{project_dir}")
    print(f"   章节文件：{project_dir}/chapters/")
    print(f"   项目配置：{project_dir}/project_config.json")
    print(f"   生成报告：{report_file}")
    print()
    
    print("🎉 多子多福系统文前五章生成完成！")
    print()
    print("📖 章节概览：")
    for chapter in chapters_info:
        print(f"   第{chapter['number']}章：{chapter['title']} ({chapter['word_count']}字)")
    
    return project_dir

if __name__ == "__main__":
    create_mock_novel()
