{"ai_models": {"primary": {"provider": "openai", "model": "claude-3-sonnet-20240229", "api_key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "base_url": "https://lazy-bear-13.deno.dev/v1", "max_tokens": 4000, "temperature": 0.8}, "secondary": {"provider": "openai", "model": "gpt-4", "api_key": "your_openai_api_key_here", "base_url": "https://api.openai.com/v1", "max_tokens": 4000, "temperature": 0.8}, "local": {"provider": "ollama", "model": "qwen2:7b", "base_url": "http://localhost:11434", "max_tokens": 4000, "temperature": 0.8}}, "generation_settings": {"retry_attempts": 3, "timeout": 60, "fallback_enabled": true, "quality_threshold": 0.7}, "prompt_settings": {"system_prompt_template": "你是一个专业的小说创作AI助手，擅长根据大纲和人物设定创作高质量的小说章节。", "max_context_length": 8000, "include_previous_chapters": 3}}