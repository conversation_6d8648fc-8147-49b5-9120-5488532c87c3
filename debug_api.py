#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API响应
"""

import requests
import json

def debug_api_response():
    """调试API响应"""
    base_url = "https://lazy-bear-13.deno.dev/v1"
    api_key = "awdsadwasdas"
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    data = {
        "model": "claude-3-sonnet-20240229",
        "messages": [
            {"role": "user", "content": "请写一个关于程序员林浩的简短故事开头，大约200字。"}
        ],
        "max_tokens": 1000,
        "temperature": 0.8
    }
    
    print("发送请求...")
    print(f"URL: {base_url}/chat/completions")
    print(f"Headers: {headers}")
    print(f"Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
    print()
    
    try:
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            result = response.json()
            print("完整响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            print()
            
            # 尝试提取内容
            if 'choices' in result and len(result['choices']) > 0:
                choice = result['choices'][0]
                print("第一个选择:")
                print(json.dumps(choice, indent=2, ensure_ascii=False))
                print()
                
                if 'message' in choice:
                    message = choice['message']
                    print("消息内容:")
                    print(json.dumps(message, indent=2, ensure_ascii=False))
                    print()
                    
                    if 'content' in message:
                        content = message['content']
                        print(f"提取的内容: '{content}'")
                        print(f"内容长度: {len(content)}")
                        print(f"内容类型: {type(content)}")
                        
                        if content:
                            print("✅ 成功获取内容")
                        else:
                            print("❌ 内容为空")
                    else:
                        print("❌ 消息中没有content字段")
                else:
                    print("❌ 选择中没有message字段")
            else:
                print("❌ 响应中没有choices字段或choices为空")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

if __name__ == "__main__":
    debug_api_response()
