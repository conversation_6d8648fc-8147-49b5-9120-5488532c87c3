{"chapter_generation": {"name": "章节生成主模板", "description": "用于生成完整章节的主要提示词模板", "system_prompt": "你是一个专业的小说创作AI助手，擅长根据大纲和人物设定创作高质量的小说章节。\\n\\n创作要求：\\n1. 严格遵循角色设定，不能出现OOC（角色崩坏）\\n2. 情节发展要符合逻辑，与前文保持一致\\n3. 对话要生动自然，符合角色性格特点\\n4. 环境描写要详细，营造沉浸感\\n5. 适当使用隐喻和象征手法\\n6. 控制节奏，张弛有度\\n7. 字数控制在2500-3500字之间\\n\\n禁止事项：\\n1. 不要使用突然、忽然等突兀的转折词\\n2. 爱情场景禁用爱、喜欢等直白词汇\\n3. 不要让角色做出违背设定的行为\\n4. 不要随意改变世界观规则", "user_prompt_template": "请根据以下设定创作一个小说章节：\\n\\n【场景定位】\\n时间：{time}\\n地点：{location}\\n天气：{weather}\\n\\n【核心事件】\\n{core_event}\\n\\n【人物目标】\\n{character_goals}\\n\\n【情感基调】\\n{emotional_tone}\\n\\n【强制元素】\\n{mandatory_elements}\\n\\n【角色信息】\\n{character_info}\\n\\n【世界观规则】\\n{worldview_rules}\\n\\n【前文回顾】\\n{previous_context}\\n\\n请创作一个{target_word_count}字左右的章节，要求：\\n1. 严格按照角色性格和行为模式描写\\n2. 包含所有强制元素\\n3. 情感曲线符合设定\\n4. 对话生动自然，符合角色特点\\n5. 环境描写与世界观设定一致\\n6. 适当埋设伏笔，为后续剧情做铺垫", "variables": {"time": "具体时间设定", "location": "地点及环境特征", "weather": "天气氛围", "core_event": "核心事件描述", "character_goals": "角色目标列表", "emotional_tone": "情感基调", "mandatory_elements": "强制包含元素", "character_info": "角色详细信息", "worldview_rules": "相关世界观规则", "previous_context": "前文情节回顾", "target_word_count": "目标字数"}}, "opening_chapter": {"name": "开篇章节模板", "description": "用于生成小说开篇章节的特殊模板", "template": "开篇章节创作要求：\n\n【世界观建立】\n1. 核心设定展示：{core_settings}\n2. 展示方式：通过{show_method}自然展现\n3. 信息密度：{information_density}\n\n【主角登场】\n1. 出场方式：{protagonist_entrance}\n2. 初始状态：{initial_state}\n3. 性格展示：{personality_display}\n\n【钩子设置】\n1. 开篇钩子：{opening_hook}\n2. 悬念布局：{suspense_layout}\n3. 读者期待：{reader_expectation}\n\n【节奏控制】\n{pacing_control}\n\n【特殊要求】\n{special_requirements}", "variables": {"core_settings": "核心设定列表", "show_method": "展示方法", "information_density": "信息密度控制", "protagonist_entrance": "主角出场方式", "initial_state": "主角初始状态", "personality_display": "性格展示方式", "opening_hook": "开篇钩子", "suspense_layout": "悬念布局", "reader_expectation": "读者期待设置", "pacing_control": "节奏控制", "special_requirements": "特殊要求"}}, "climax_chapter": {"name": "高潮章节模板", "description": "用于生成高潮章节的特殊模板", "template": "高潮章节创作要求：\n\n【冲突升级】\n1. 矛盾激化：{conflict_escalation}\n2. stakes提升：{stakes_raising}\n3. 紧张感营造：{tension_building}\n\n【角色蜕变】\n1. 主角成长：{protagonist_growth}\n2. 能力觉醒：{ability_awakening}\n3. 心理突破：{psychological_breakthrough}\n\n【情节爆发】\n1. 关键转折：{key_turning_point}\n2. 真相揭露：{truth_revelation}\n3. 决战时刻：{final_confrontation}\n\n【情感高潮】\n{emotional_climax}\n\n【伏笔回收】\n{foreshadowing_payoff}", "variables": {"conflict_escalation": "矛盾激化方式", "stakes_raising": "stakes提升", "tension_building": "紧张感营造", "protagonist_growth": "主角成长", "ability_awakening": "能力觉醒", "psychological_breakthrough": "心理突破", "key_turning_point": "关键转折", "truth_revelation": "真相揭露", "final_confrontation": "决战时刻", "emotional_climax": "情感高潮", "foreshadowing_payoff": "伏笔回收"}}, "character_development": {"name": "角色发展章节模板", "description": "专注于角色发展的章节模板", "template": "角色发展章节：{character_name}\n\n【发展目标】\n1. 成长方向：{growth_direction}\n2. 突破点：{breakthrough_point}\n3. 变化体现：{change_manifestation}\n\n【内心历程】\n1. 心理状态：{psychological_state}\n2. 内心冲突：{internal_conflict}\n3. 认知转变：{cognitive_shift}\n\n【外在表现】\n1. 行为变化：{behavior_change}\n2. 关系影响：{relationship_impact}\n3. 能力提升：{ability_improvement}\n\n【发展催化剂】\n{development_catalyst}\n\n【后续影响】\n{future_impact}", "variables": {"character_name": "角色名称", "growth_direction": "成长方向", "breakthrough_point": "突破点", "change_manifestation": "变化体现", "psychological_state": "心理状态", "internal_conflict": "内心冲突", "cognitive_shift": "认知转变", "behavior_change": "行为变化", "relationship_impact": "关系影响", "ability_improvement": "能力提升", "development_catalyst": "发展催化剂", "future_impact": "后续影响"}}, "foreshadowing_chapter": {"name": "伏笔埋设章节模板", "description": "专门用于埋设重要伏笔的章节模板", "template": "伏笔埋设章节\n\n【伏笔清单】\n{foreshadowing_list}\n\n【埋设方式】\n1. 主要伏笔：{main_foreshadowing_method}\n2. 次要伏笔：{secondary_foreshadowing_method}\n3. 隐藏伏笔：{hidden_foreshadowing_method}\n\n【自然度要求】\n1. 情节融合：{plot_integration}\n2. 角色动机：{character_motivation}\n3. 读者感知：{reader_perception}\n\n【回收规划】\n{payoff_planning}\n\n【注意事项】\n{precautions}", "variables": {"foreshadowing_list": "伏笔清单", "main_foreshadowing_method": "主要伏笔埋设方式", "secondary_foreshadowing_method": "次要伏笔埋设方式", "hidden_foreshadowing_method": "隐藏伏笔埋设方式", "plot_integration": "情节融合要求", "character_motivation": "角色动机", "reader_perception": "读者感知控制", "payoff_planning": "回收规划", "precautions": "注意事项"}}, "relationship_chapter": {"name": "关系发展章节模板", "description": "专注于角色关系发展的章节模板", "template": "关系发展章节：{character_a} × {character_b}\n\n【关系现状】\n1. 当前关系：{current_relationship}\n2. 关系问题：{relationship_issues}\n3. 发展需求：{development_needs}\n\n【发展契机】\n1. 触发事件：{trigger_event}\n2. 外部压力：{external_pressure}\n3. 内在动机：{internal_motivation}\n\n【互动设计】\n1. 对话重点：{dialogue_focus}\n2. 行为表现：{behavior_display}\n3. 情感变化：{emotional_change}\n\n【关系转变】\n{relationship_transformation}\n\n【后续影响】\n{future_implications}", "variables": {"character_a": "角色A", "character_b": "角色B", "current_relationship": "当前关系状态", "relationship_issues": "关系问题", "development_needs": "发展需求", "trigger_event": "触发事件", "external_pressure": "外部压力", "internal_motivation": "内在动机", "dialogue_focus": "对话重点", "behavior_display": "行为表现", "emotional_change": "情感变化", "relationship_transformation": "关系转变", "future_implications": "后续影响"}}}