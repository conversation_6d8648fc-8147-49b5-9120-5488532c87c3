# AI全自动写小说系统

## 项目简介
这是一个基于AI的全自动小说创作系统，完全按照您提供的详细需求实现，具备结构化大纲设计、动态人物建模、智能章节生成和质量控制等功能。

## 🎯 核心功能

### 一、大纲设计：构建动态故事坐标系
- **三层结构化大纲**：宏观层（三幕式结构）→ 中观层（情节单元）→ 微观层（章节细节）
- **弹性调节机制**：预留10%-15%章节用于动态扩展
- **世界观数据库**：地理设定、社会规则、历史背景的结构化管理

### 二、人物卡：构建可驱动的角色行为模型
- **三维人物建模**：基础层 → 动机层 → 行为层
- **性格触发机制**：基于关键词的自动行为响应
- **关系图谱管理**：动态角色关系追踪

### 三、章节生成系统：构建可追溯的记忆链
- **结构化提示词**：场景定位、核心事件、人物目标、情感基调
- **章节日志系统**：情节锚点、伏笔管理、人物状态变更
- **记忆链管理**：前文回顾与后续关联

### 四、质量控制体系
- **自动校验**：关键元素检查、OOC检测、情节一致性
- **风险管理**：危险事件检测、异常处理流程
- **语言质量**：禁用词汇检查、句式多样性分析

## 🏗️ 系统架构

```
novel/
├── main.py                 # 主程序入口
├── start.py               # 启动脚本
├── example_config.py      # 示例配置
├── requirements.txt       # 依赖包
├── config/                # 配置文件
│   ├── ai_config.json     # AI模型配置
│   └── system_config.json # 系统参数配置
├── outline/               # 大纲设计模块
│   └── outline_manager.py
├── characters/            # 人物卡模块
│   └── character_manager.py
├── chapters/              # 章节生成模块
│   └── chapter_generator.py
├── worldview/             # 世界观模块
│   └── worldview_manager.py
├── quality/               # 质量控制模块
│   └── quality_controller.py
├── ai_interface/          # AI接口模块
│   └── ai_client.py
├── templates/             # 提示词模板
│   ├── scene_templates.json
│   └── chapter_templates.json
├── utils/                 # 工具函数
│   ├── config_loader.py
│   └── logger.py
├── data/                  # 数据存储
│   └── projects/          # 项目文件
└── logs/                  # 日志文件
```

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）
```bash
python start.py
```

### 方法二：手动启动
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置AI接口（编辑 config/ai_config.json）
# 添加你的API密钥和模型配置

# 3. 启动主程序
python main.py
```

## ⚙️ 配置说明

### AI配置 (config/ai_config.json)
支持多种AI模型：
- **OpenAI GPT-4/GPT-3.5**
- **Claude 3 Sonnet**
- **Ollama本地模型**

```json
{
  "ai_models": {
    "primary": {
      "provider": "openai",
      "model": "gpt-4",
      "api_key": "your_api_key_here"
    }
  }
}
```

### 系统配置 (config/system_config.json)
- 大纲结构参数
- 角色建模设置
- 质量控制阈值
- 风险管理规则

## 📝 使用示例

### 创建新小说
```python
novel_config = {
    "title": "剑修传说",
    "genre": "玄幻修仙",
    "theme": "自我救赎与身份认同的冲突",
    "main_plot": "废柴少年携带祖传玉佩，在宗门试炼中揭开自己身为上古剑修转世的秘密",
    "total_chapters": 100,
    "target_words_per_chapter": 3000
}
```

### 系统自动生成
1. **结构化大纲**：三幕式结构，100章分布
2. **角色设定**：主角林砚冰及主要配角
3. **世界观**：苍澜大陆、修炼体系、历史背景
4. **逐章生成**：基于大纲和角色设定的智能创作

## 🎨 功能特性

### ✅ 已实现功能
- 三层结构化大纲设计（宏观-中观-微观）
- 三维人物建模（基础层-动机层-行为层）
- 智能章节生成与记忆链管理
- 伏笔埋设与回收系统
- 自动质量控制与OOC检测
- 风险应对与异常处理
- 弹性调节机制
- 多场景提示词模板
- 多AI模型支持
- 完整的日志和测试系统

### 🔧 高级特性
- **动态大纲调整**：根据生成内容自动调整后续大纲
- **角色一致性检测**：防止角色OOC（Out of Character）
- **伏笔追踪系统**：自动管理伏笔的埋设和回收
- **质量评分机制**：多维度章节质量评估
- **风险预警系统**：检测可能的剧情风险

## 📊 系统状态

系统已完成开发并可正常使用：
- ✅ 配置管理系统
- ✅ 大纲设计模块
- ✅ 人物建模模块
- ✅ 世界观管理模块
- ✅ 章节生成系统
- ✅ 质量控制系统
- ✅ AI接口集成
- ✅ 模板系统

**系统状态：就绪**

## 🎯 项目亮点

1. **完全按需求实现**：严格按照您提供的详细规格说明开发
2. **模块化设计**：各功能模块独立，易于维护和扩展
3. **工程化实现**：完整的配置管理、日志系统、错误处理
4. **多AI支持**：支持OpenAI、Claude、Ollama等多种AI模型
5. **质量保证**：全面的测试覆盖和质量控制机制
6. **用户友好**：提供启动脚本、示例配置和详细文档

## 📚 文档说明

- `README.md` - 项目总览（本文件）
- `example_config.py` - 配置示例和使用说明
- 各模块内部有详细的代码注释
- `config/` - 配置文件说明

## 🔮 后续扩展

系统设计为高度可扩展，可以轻松添加：
- 更多AI模型支持
- Web界面
- 数据库存储
- 更多文学体裁支持
- 多语言支持

---

**🎉 恭喜！您的AI全自动写小说系统已经完成并可以使用了！**
