#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI全自动写小说系统 - 主程序入口
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from outline.outline_manager import OutlineManager
from characters.character_manager import CharacterManager
from chapters.chapter_generator import ChapterGenerator
from worldview.worldview_manager import WorldviewManager
from quality.quality_controller import QualityController
from ai_interface.ai_client import AIClient
from utils.logger import setup_logger
from utils.config_loader import ConfigLoader

class NovelWritingSystem:
    """AI小说创作系统主类"""
    
    def __init__(self):
        """初始化系统"""
        self.config = ConfigLoader()
        self.logger = setup_logger()
        
        # 初始化各个模块
        self.ai_client = AIClient(self.config.get_ai_config())
        self.outline_manager = OutlineManager(self.config)
        self.character_manager = CharacterManager(self.config)
        self.worldview_manager = WorldviewManager(self.config)
        self.chapter_generator = ChapterGenerator(
            self.ai_client, 
            self.outline_manager,
            self.character_manager,
            self.worldview_manager
        )
        self.quality_controller = QualityController(self.config)
        
        self.logger.info("AI小说创作系统初始化完成")
    
    def create_new_novel(self, novel_config):
        """创建新小说项目"""
        try:
            self.logger.info(f"开始创建新小说项目: {novel_config.get('title', '未命名')}")
            
            # 1. 创建项目目录
            project_dir = self._create_project_directory(novel_config['title'])
            
            # 2. 初始化大纲
            outline = self.outline_manager.create_outline(novel_config)
            
            # 3. 创建世界观
            worldview = self.worldview_manager.create_worldview(novel_config)
            
            # 4. 创建主要角色
            characters = self.character_manager.create_main_characters(novel_config)
            
            # 5. 保存项目配置
            self._save_project_config(project_dir, {
                'outline': outline,
                'worldview': worldview,
                'characters': characters,
                'config': novel_config
            })
            
            self.logger.info(f"新小说项目创建完成: {project_dir}")
            return project_dir
            
        except Exception as e:
            self.logger.error(f"创建新小说项目失败: {str(e)}")
            raise
    
    def generate_chapter(self, project_dir, chapter_num):
        """生成指定章节"""
        try:
            self.logger.info(f"开始生成第{chapter_num}章")
            
            # 加载项目配置
            project_config = self._load_project_config(project_dir)
            
            # 生成章节
            chapter = self.chapter_generator.generate_chapter(
                project_config, chapter_num
            )
            
            # 质量控制检查
            quality_result = self.quality_controller.check_chapter(
                chapter, project_config
            )
            
            if quality_result['passed']:
                # 保存章节
                self._save_chapter(project_dir, chapter_num, chapter)
                self.logger.info(f"第{chapter_num}章生成完成")
                return chapter
            else:
                self.logger.warning(f"第{chapter_num}章质量检查未通过: {quality_result['issues']}")
                # 重新生成或修正
                return self._fix_chapter(chapter, quality_result['issues'])
                
        except Exception as e:
            self.logger.error(f"生成第{chapter_num}章失败: {str(e)}")
            raise
    
    def generate_full_novel(self, novel_config):
        """生成完整小说"""
        try:
            self.logger.info("开始生成完整小说")
            
            # 创建项目
            project_dir = self.create_new_novel(novel_config)
            
            # 获取总章节数
            project_config = self._load_project_config(project_dir)
            total_chapters = project_config['outline']['total_chapters']
            
            # 逐章生成
            for chapter_num in range(1, total_chapters + 1):
                chapter = self.generate_chapter(project_dir, chapter_num)
                
                # 每5章进行一次逻辑梳检
                if chapter_num % 5 == 0:
                    self._logic_review(project_dir, chapter_num)
            
            self.logger.info("完整小说生成完成")
            return project_dir
            
        except Exception as e:
            self.logger.error(f"生成完整小说失败: {str(e)}")
            raise
    
    def _create_project_directory(self, title):
        """创建项目目录"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        project_name = f"{title}_{timestamp}"
        project_dir = Path("data/projects") / project_name
        project_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        (project_dir / "chapters").mkdir(exist_ok=True)
        (project_dir / "logs").mkdir(exist_ok=True)
        (project_dir / "backups").mkdir(exist_ok=True)
        
        return project_dir
    
    def _save_project_config(self, project_dir, config):
        """保存项目配置"""
        config_file = project_dir / "project_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def _load_project_config(self, project_dir):
        """加载项目配置"""
        config_file = project_dir / "project_config.json"
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _save_chapter(self, project_dir, chapter_num, chapter):
        """保存章节"""
        chapter_file = project_dir / "chapters" / f"chapter_{chapter_num:03d}.json"
        with open(chapter_file, 'w', encoding='utf-8') as f:
            json.dump(chapter, f, ensure_ascii=False, indent=2)
    
    def _fix_chapter(self, chapter, issues):
        """修正章节问题"""
        # TODO: 实现章节修正逻辑
        pass
    
    def _logic_review(self, project_dir, current_chapter):
        """逻辑梳检"""
        # TODO: 实现逻辑梳检功能
        pass

def main():
    """主函数"""
    print("=== AI全自动写小说系统 ===")
    print("1. 创建新小说")
    print("2. 生成章节")
    print("3. 生成完整小说")
    print("4. 退出")
    
    system = NovelWritingSystem()
    
    while True:
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == '1':
            # 创建新小说的示例配置
            novel_config = {
                'title': '剑修传说',
                'genre': '玄幻',
                'theme': '自我救赎与身份认同的冲突',
                'main_plot': '废柴少年携带祖传玉佩，在宗门试炼中揭开自己身为上古剑修转世的秘密',
                'total_chapters': 100,
                'target_words_per_chapter': 3000
            }
            
            try:
                project_dir = system.create_new_novel(novel_config)
                print(f"新小说项目创建成功: {project_dir}")
            except Exception as e:
                print(f"创建失败: {str(e)}")
        
        elif choice == '2':
            project_dir = input("请输入项目目录: ").strip()
            chapter_num = int(input("请输入章节号: ").strip())
            
            try:
                chapter = system.generate_chapter(project_dir, chapter_num)
                print(f"第{chapter_num}章生成成功")
            except Exception as e:
                print(f"生成失败: {str(e)}")
        
        elif choice == '3':
            novel_config = {
                'title': input("请输入小说标题: ").strip(),
                'genre': input("请输入小说类型: ").strip(),
                'theme': input("请输入主题内核: ").strip(),
                'main_plot': input("请输入故事主线: ").strip(),
                'total_chapters': int(input("请输入总章节数: ").strip()),
                'target_words_per_chapter': int(input("请输入每章目标字数: ").strip())
            }
            
            try:
                project_dir = system.generate_full_novel(novel_config)
                print(f"完整小说生成成功: {project_dir}")
            except Exception as e:
                print(f"生成失败: {str(e)}")
        
        elif choice == '4':
            print("感谢使用AI小说创作系统！")
            break
        
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
