2025-06-08 14:44:34 - OutlineManager - INFO - 开始创建结构化大纲
2025-06-08 14:44:34 - OutlineManager - INFO - 大纲创建完成，共3幕，10章
2025-06-08 14:44:34 - CharacterManager - INFO - 开始创建主要角色
2025-06-08 14:44:34 - CharacterManager - INFO - 角色创建完成，共5个主要角色
2025-06-08 14:44:34 - WorldviewManager - INFO - 开始创建世界观设定
2025-06-08 14:44:34 - WorldviewManager - INFO - 世界观设定创建完成
2025-06-08 14:44:34 - QualityController - INFO - 开始质量检查：第1章
2025-06-08 14:44:34 - QualityController - INFO - 质量检查完成，评分：0.50，通过：False
2025-06-08 14:45:27 - OutlineManager - INFO - 开始创建结构化大纲
2025-06-08 14:45:27 - OutlineManager - INFO - 大纲创建完成，共3幕，10章
2025-06-08 14:45:27 - CharacterManager - INFO - 开始创建主要角色
2025-06-08 14:45:27 - CharacterManager - INFO - 角色创建完成，共5个主要角色
2025-06-08 14:45:27 - WorldviewManager - INFO - 开始创建世界观设定
2025-06-08 14:45:27 - WorldviewManager - INFO - 世界观设定创建完成
2025-06-08 14:45:27 - QualityController - INFO - 开始质量检查：第1章
2025-06-08 14:45:27 - QualityController - INFO - 质量检查完成，评分：0.50，通过：False
2025-06-08 15:00:17 - NovelSystem - INFO - AI小说创作系统初始化完成
2025-06-08 15:00:17 - NovelSystem - INFO - 开始创建新小说项目: 多子多福系统
2025-06-08 15:00:17 - OutlineManager - INFO - 开始创建结构化大纲
2025-06-08 15:00:17 - OutlineManager - INFO - 大纲创建完成，共3幕，100章
2025-06-08 15:00:17 - WorldviewManager - INFO - 开始创建世界观设定
2025-06-08 15:00:17 - WorldviewManager - INFO - 世界观设定创建完成
2025-06-08 15:00:17 - CharacterManager - INFO - 开始创建主要角色
2025-06-08 15:00:17 - CharacterManager - INFO - 角色创建完成，共5个主要角色
2025-06-08 15:00:17 - NovelSystem - INFO - 新小说项目创建完成: data\projects\多子多福系统_20250608_150017
2025-06-08 15:00:17 - NovelSystem - INFO - 开始生成第1章
2025-06-08 15:00:17 - ChapterGenerator - INFO - 开始生成第1章
2025-06-08 15:00:18 - AIClient - WARNING - AI调用失败 (尝试 1/3): 404 Client Error: Not Found for url: https://lazy-bear-13.deno.dev/v1/messages
2025-06-08 15:00:18 - AIClient - INFO - 切换到备用模型: secondary
2025-06-08 15:01:01 - AIClient - WARNING - AI调用失败 (尝试 2/3): HTTPSConnectionPool(host='api.openai.com', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000167A3A85370>: Failed to establish a new connection: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。'))
2025-06-08 15:01:01 - AIClient - INFO - 切换到备用模型: local
2025-06-08 15:01:07 - AIClient - WARNING - AI调用失败 (尝试 3/3): HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000167A3AE3040>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-06-08 15:01:07 - NovelSystem - ERROR - 生成第1章失败: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000167A3AE3040>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-06-08 15:01:07 - NovelSystem - INFO - 开始生成第2章
2025-06-08 15:01:07 - ChapterGenerator - INFO - 开始生成第2章
2025-06-08 15:01:11 - AIClient - WARNING - AI调用失败 (尝试 1/3): HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000167A3AE3850>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-06-08 15:01:11 - AIClient - INFO - 切换到备用模型: primary
2025-06-08 15:01:13 - AIClient - WARNING - AI调用失败 (尝试 2/3): HTTPSConnectionPool(host='lazy-bear-13.deno.dev', port=443): Max retries exceeded with url: /v1/messages (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))
2025-06-08 15:01:13 - AIClient - INFO - 切换到备用模型: secondary
2025-06-08 15:01:57 - AIClient - WARNING - AI调用失败 (尝试 3/3): HTTPSConnectionPool(host='api.openai.com', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000167A3A79430>: Failed to establish a new connection: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。'))
2025-06-08 15:01:57 - NovelSystem - ERROR - 生成第2章失败: HTTPSConnectionPool(host='api.openai.com', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000167A3A79430>: Failed to establish a new connection: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。'))
2025-06-08 15:01:57 - NovelSystem - INFO - 开始生成第3章
2025-06-08 15:01:57 - ChapterGenerator - INFO - 开始生成第3章
2025-06-08 15:02:39 - AIClient - WARNING - AI调用失败 (尝试 1/3): HTTPSConnectionPool(host='api.openai.com', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000167A3AE3640>: Failed to establish a new connection: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。'))
2025-06-08 15:02:39 - AIClient - INFO - 切换到备用模型: local
2025-06-08 15:02:44 - AIClient - WARNING - AI调用失败 (尝试 2/3): HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000167A3B09040>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-06-08 15:02:44 - AIClient - INFO - 切换到备用模型: primary
2025-06-08 15:02:48 - AIClient - WARNING - AI调用失败 (尝试 3/3): 404 Client Error: Not Found for url: https://lazy-bear-13.deno.dev/v1/messages
2025-06-08 15:02:48 - NovelSystem - ERROR - 生成第3章失败: 404 Client Error: Not Found for url: https://lazy-bear-13.deno.dev/v1/messages
2025-06-08 15:02:48 - NovelSystem - INFO - 开始生成第4章
2025-06-08 15:02:48 - ChapterGenerator - INFO - 开始生成第4章
2025-06-08 15:02:49 - AIClient - WARNING - AI调用失败 (尝试 1/3): 404 Client Error: Not Found for url: https://lazy-bear-13.deno.dev/v1/messages
2025-06-08 15:02:49 - AIClient - INFO - 切换到备用模型: secondary
2025-06-08 15:03:32 - AIClient - WARNING - AI调用失败 (尝试 2/3): HTTPSConnectionPool(host='api.openai.com', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000167A3A85220>: Failed to establish a new connection: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。'))
2025-06-08 15:03:32 - AIClient - INFO - 切换到备用模型: local
2025-06-08 15:03:38 - AIClient - WARNING - AI调用失败 (尝试 3/3): HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000167A3AE3040>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-06-08 15:03:38 - NovelSystem - ERROR - 生成第4章失败: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000167A3AE3040>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-06-08 15:03:38 - NovelSystem - INFO - 开始生成第5章
2025-06-08 15:03:38 - ChapterGenerator - INFO - 开始生成第5章
2025-06-08 15:03:42 - AIClient - WARNING - AI调用失败 (尝试 1/3): HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000167A3A85190>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-06-08 15:03:42 - AIClient - INFO - 切换到备用模型: primary
2025-06-08 15:03:45 - AIClient - WARNING - AI调用失败 (尝试 2/3): 404 Client Error: Not Found for url: https://lazy-bear-13.deno.dev/v1/messages
2025-06-08 15:03:45 - AIClient - INFO - 切换到备用模型: secondary
2025-06-08 15:04:29 - AIClient - WARNING - AI调用失败 (尝试 3/3): HTTPSConnectionPool(host='api.openai.com', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000167A3A792E0>: Failed to establish a new connection: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。'))
2025-06-08 15:04:29 - NovelSystem - ERROR - 生成第5章失败: HTTPSConnectionPool(host='api.openai.com', port=443): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000167A3A792E0>: Failed to establish a new connection: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。'))
2025-06-08 15:06:05 - NovelSystem - INFO - AI小说创作系统初始化完成
2025-06-08 15:06:05 - NovelSystem - INFO - 开始创建新小说项目: 多子多福系统
2025-06-08 15:06:05 - OutlineManager - INFO - 开始创建结构化大纲
2025-06-08 15:06:05 - OutlineManager - INFO - 大纲创建完成，共3幕，100章
2025-06-08 15:06:05 - WorldviewManager - INFO - 开始创建世界观设定
2025-06-08 15:06:05 - WorldviewManager - INFO - 世界观设定创建完成
2025-06-08 15:06:05 - CharacterManager - INFO - 开始创建主要角色
2025-06-08 15:06:05 - CharacterManager - INFO - 角色创建完成，共5个主要角色
2025-06-08 15:06:05 - NovelSystem - INFO - 新小说项目创建完成: data\projects\多子多福系统_20250608_150605
2025-06-08 15:06:05 - NovelSystem - INFO - 开始生成第1章
2025-06-08 15:06:05 - ChapterGenerator - INFO - 开始生成第1章
2025-06-08 15:06:06 - ChapterGenerator - INFO - 第1章生成完成，字数：0
2025-06-08 15:06:06 - QualityController - INFO - 开始质量检查：第1章
2025-06-08 15:06:06 - QualityController - INFO - 质量检查完成，评分：0.25，通过：False
2025-06-08 15:06:06 - NovelSystem - WARNING - 第1章质量检查未通过: [{'type': 'missing_mandatory_element', 'severity': 'high', 'description': '缺少必需的伏笔元素：祖传玉佩的神秘光芒', 'location': '全文', 'suggestion': '请在章节中加入相关描述：祖传玉佩的神秘光芒'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：修炼体系', 'location': '全文', 'suggestion': '请在章节中展示相关设定：修炼体系'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：宗门规则', 'location': '全文', 'suggestion': '请在章节中展示相关设定：宗门规则'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：祖传玉佩', 'location': '全文', 'suggestion': '请在章节中展示相关设定：祖传玉佩'}]
2025-06-08 15:06:06 - NovelSystem - INFO - 开始生成第2章
2025-06-08 15:06:06 - ChapterGenerator - INFO - 开始生成第2章
2025-06-08 15:06:07 - ChapterGenerator - INFO - 第2章生成完成，字数：0
2025-06-08 15:06:07 - QualityController - INFO - 开始质量检查：第2章
2025-06-08 15:06:07 - QualityController - INFO - 质量检查完成，评分：0.25，通过：False
2025-06-08 15:06:07 - NovelSystem - WARNING - 第2章质量检查未通过: [{'type': 'missing_mandatory_element', 'severity': 'high', 'description': '缺少必需的伏笔元素：祖传玉佩的神秘光芒', 'location': '全文', 'suggestion': '请在章节中加入相关描述：祖传玉佩的神秘光芒'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：修炼体系', 'location': '全文', 'suggestion': '请在章节中展示相关设定：修炼体系'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：宗门规则', 'location': '全文', 'suggestion': '请在章节中展示相关设定：宗门规则'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：祖传玉佩', 'location': '全文', 'suggestion': '请在章节中展示相关设定：祖传玉佩'}]
2025-06-08 15:06:07 - NovelSystem - INFO - 开始生成第3章
2025-06-08 15:06:07 - ChapterGenerator - INFO - 开始生成第3章
2025-06-08 15:06:08 - ChapterGenerator - INFO - 第3章生成完成，字数：0
2025-06-08 15:06:08 - QualityController - INFO - 开始质量检查：第3章
2025-06-08 15:06:08 - QualityController - INFO - 质量检查完成，评分：0.25，通过：False
2025-06-08 15:06:08 - NovelSystem - WARNING - 第3章质量检查未通过: [{'type': 'missing_mandatory_element', 'severity': 'high', 'description': '缺少必需的伏笔元素：祖传玉佩的神秘光芒', 'location': '全文', 'suggestion': '请在章节中加入相关描述：祖传玉佩的神秘光芒'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：修炼体系', 'location': '全文', 'suggestion': '请在章节中展示相关设定：修炼体系'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：宗门规则', 'location': '全文', 'suggestion': '请在章节中展示相关设定：宗门规则'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：祖传玉佩', 'location': '全文', 'suggestion': '请在章节中展示相关设定：祖传玉佩'}]
2025-06-08 15:06:08 - NovelSystem - INFO - 开始生成第4章
2025-06-08 15:06:08 - ChapterGenerator - INFO - 开始生成第4章
2025-06-08 15:06:08 - ChapterGenerator - INFO - 第4章生成完成，字数：0
2025-06-08 15:06:08 - QualityController - INFO - 开始质量检查：第4章
2025-06-08 15:06:08 - QualityController - INFO - 质量检查完成，评分：0.25，通过：False
2025-06-08 15:06:08 - NovelSystem - WARNING - 第4章质量检查未通过: [{'type': 'missing_mandatory_element', 'severity': 'high', 'description': '缺少必需的伏笔元素：祖传玉佩的神秘光芒', 'location': '全文', 'suggestion': '请在章节中加入相关描述：祖传玉佩的神秘光芒'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：修炼体系', 'location': '全文', 'suggestion': '请在章节中展示相关设定：修炼体系'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：宗门规则', 'location': '全文', 'suggestion': '请在章节中展示相关设定：宗门规则'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：祖传玉佩', 'location': '全文', 'suggestion': '请在章节中展示相关设定：祖传玉佩'}]
2025-06-08 15:06:08 - NovelSystem - INFO - 开始生成第5章
2025-06-08 15:06:08 - ChapterGenerator - INFO - 开始生成第5章
2025-06-08 15:06:09 - ChapterGenerator - INFO - 第5章生成完成，字数：0
2025-06-08 15:06:09 - QualityController - INFO - 开始质量检查：第5章
2025-06-08 15:06:09 - QualityController - INFO - 质量检查完成，评分：0.25，通过：False
2025-06-08 15:06:09 - NovelSystem - WARNING - 第5章质量检查未通过: [{'type': 'missing_mandatory_element', 'severity': 'high', 'description': '缺少必需的伏笔元素：祖传玉佩的神秘光芒', 'location': '全文', 'suggestion': '请在章节中加入相关描述：祖传玉佩的神秘光芒'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：修炼体系', 'location': '全文', 'suggestion': '请在章节中展示相关设定：修炼体系'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：宗门规则', 'location': '全文', 'suggestion': '请在章节中展示相关设定：宗门规则'}, {'type': 'missing_mandatory_element', 'severity': 'medium', 'description': '缺少必需的设定植入：祖传玉佩', 'location': '全文', 'suggestion': '请在章节中展示相关设定：祖传玉佩'}]
2025-06-08 15:15:19 - NovelSystem - INFO - AI小说创作系统初始化完成
2025-06-08 15:15:19 - NovelSystem - INFO - 开始创建新小说项目: 多子多福系统
2025-06-08 15:15:19 - OutlineManager - INFO - 开始创建结构化大纲
2025-06-08 15:15:19 - OutlineManager - INFO - 大纲创建完成，共3幕，100章
2025-06-08 15:15:19 - WorldviewManager - INFO - 开始创建世界观设定
2025-06-08 15:15:19 - WorldviewManager - INFO - 世界观设定创建完成
2025-06-08 15:15:19 - CharacterManager - INFO - 开始创建主要角色
2025-06-08 15:15:19 - CharacterManager - INFO - 角色创建完成，共5个主要角色
2025-06-08 15:15:19 - NovelSystem - INFO - 新小说项目创建完成: data\projects\多子多福系统_20250608_151519
2025-06-08 15:15:19 - NovelSystem - INFO - 开始生成第1章
2025-06-08 15:15:19 - ChapterGenerator - INFO - 开始生成第1章
