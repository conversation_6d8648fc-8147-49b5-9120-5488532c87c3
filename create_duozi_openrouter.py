#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用OpenRouter API创建多子多福系统文
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from ai_interface.ai_client import AIClient
from utils.config_loader import ConfigLoader

def create_duozi_novel():
    """创建多子多福系统文"""
    print("🎉 使用DeepSeek R1模型创建多子多福系统文")
    print("=" * 60)
    
    try:
        # 初始化AI客户端
        config = ConfigLoader()
        ai_config = config.get_ai_config()
        client = AIClient(ai_config)
        
        print(f"✅ AI模型: {ai_config['ai_models']['primary']['model']}")
        print(f"✅ 提供商: OpenRouter")
        print()
        
        # 创建项目目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        project_name = f"多子多福系统_DeepSeek_{timestamp}"
        project_dir = Path("data/projects") / project_name
        project_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 项目目录: {project_dir}")
        print()
        
        # 生成第一章
        print("📝 开始生成第一章...")
        print("-" * 40)
        
        # 系统提示词
        system_prompt = """你是一个专业的中文网络小说创作AI助手，擅长创作都市系统文。你的创作风格：
1. 情节紧凑，节奏明快
2. 人物性格鲜明，对话自然
3. 系统设定合理有趣
4. 语言流畅，符合网文读者喜好
5. 适当的爽点和反转"""

        # 第一章创作提示词
        chapter_prompt = """请创作小说《多子多福系统》的第一章，要求：

【小说设定】
- 类型：都市系统文
- 主角：林浩，28岁程序员，善良负责任但内向，月薪8000，单身
- 女主：苏晴雨，26岁小学教师，温柔善良，林浩的大学同学
- 系统：多子多福系统，每生一个孩子获得递增奖励（金钱、技能、身体强化等）

【第一章要求】
- 标题：平凡的程序员
- 展现林浩深夜加班的平凡生活
- 描写都市打工人的真实状态
- 体现内心的孤独和对未来的迷茫
- 为后续系统降临做铺垫
- 字数：3000-4000字
- 风格：真实感人，有代入感

【创作要点】
1. 开头要抓人，展现主角的困境
2. 细节描写要生动，让读者有共鸣
3. 内心独白要真实，体现现代人的焦虑
4. 结尾要有悬念，为下一章做铺垫
5. 语言要流畅自然，符合网文风格

请创作完整的第一章内容："""

        # 生成内容
        content = client.generate_text(chapter_prompt, system_prompt)
        
        if content and len(content) > 1000:
            print("✅ 第一章生成成功！")
            print(f"   字数: {len(content)}")
            print()
            
            # 保存章节文件
            chapter_file = project_dir / "第一章_平凡的程序员.txt"
            with open(chapter_file, 'w', encoding='utf-8') as f:
                f.write("《多子多福系统》\n")
                f.write("作者：AI创作（DeepSeek R1模型）\n")
                f.write("=" * 50 + "\n\n")
                f.write("第一章 平凡的程序员\n\n")
                f.write(content)
            
            # 保存项目信息
            import json
            project_info = {
                "title": "多子多福系统",
                "genre": "都市系统文",
                "ai_model": "deepseek/deepseek-r1-0528:free",
                "provider": "OpenRouter",
                "protagonist": "林浩（28岁程序员）",
                "heroine": "苏晴雨（26岁小学教师）",
                "created_at": datetime.now().isoformat(),
                "chapters": [
                    {
                        "number": 1,
                        "title": "平凡的程序员",
                        "word_count": len(content),
                        "file": "第一章_平凡的程序员.txt"
                    }
                ]
            }
            
            info_file = project_dir / "project_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(project_info, f, ensure_ascii=False, indent=2)
            
            print("📖 第一章内容预览：")
            print("=" * 60)
            print("第一章 平凡的程序员")
            print()
            
            # 显示前500字作为预览
            preview = content[:500] + "..." if len(content) > 500 else content
            print(preview)
            
            print("=" * 60)
            print()
            print("📁 文件保存位置：")
            print(f"   项目目录: {project_dir}")
            print(f"   第一章文件: {chapter_file}")
            print(f"   项目信息: {info_file}")
            print()
            print("💡 提示：完整内容请查看文件，这里只显示前500字预览")
            
            return True
            
        else:
            print("❌ 第一章生成失败")
            print(f"   返回内容: {content}")
            return False
            
    except Exception as e:
        print(f"❌ 创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_duozi_novel()
    
    if success:
        print("🎊 多子多福系统文第一章创建完成！")
        print("这是由DeepSeek R1模型创作的高质量原创内容。")
        print()
        print("🚀 接下来您可以：")
        print("1. 查看完整的第一章内容")
        print("2. 继续生成第二章")
        print("3. 使用完整的小说创作系统")
    else:
        print("😔 创建失败，请检查配置后重试。")
